import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:tx_im/user.dart';
import '../pages/login/login.dart';
import '../utils/sharedPreferences_util.dart';
import 'package:flutter/cupertino.dart';
bool isShowEndDialog = false;
class UserOption {
  static String tokenKey = "";
  static String token = "";
  static String roleTag = ""; // 用户角色
  static String tel = '';

  static cleanData() {
    tokenKey = "";
    token = "";
    roleTag = "";
    tel = "";
    TimUser.loginToken = "";
  }

  static cleanLocalData() {
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.tokenKey);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.token);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.roleTag);
    SharedPreferencesUtil.deleteData(SharedPreferencesUtil.tel);
  }
  static showDeliverDialog(BuildContext context,String content,cancelLogin) {
    if (isShowEndDialog) return;
    isShowEndDialog = true;
    showCupertinoDialog(
        context: context,
        builder: (BuildContext context) => CupertinoAlertDialog(
          title:  Stack(
            children: [
              // 标题文本居中
              Align(
                alignment: Alignment.center,
                child: Text('提示'),
              ),
              // 关闭按钮在右上角
              Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    isShowEndDialog = false;
                  },
                  child: Icon(CupertinoIcons.clear, size: 20),
                ),
              ),
            ],
          ),
          content: Padding(
            padding: EdgeInsets.only(top: 20), // 增加顶部间距避免内容重叠
            child: Column(
              children: [
                Text(content),
                Padding(padding:EdgeInsets.only(top: 10) ),
                Text('联系我们——************')
              ],
            ),
          ),
          actions: <Widget>[
            CupertinoDialogAction(
                child: Text('切换账号'),
                onPressed: () {
                  Navigator.of(context).pop();
                  isShowEndDialog = false;
                  cancelLogin();
                })
          ],
        ));
  }
  /// 从本地储存中取token
  static Future initToken() async {
    String key = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.tokenKey);
    if (key != null && key != "" && key != SharedPreferencesUtil.tokenKey) {
      tokenKey = key;
    }
    String t = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.token);
    if (t != null && t != "" && t != SharedPreferencesUtil.token) {
      token = t;
    }
    String role = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.roleTag);
    if (role != null && role != "" && role != SharedPreferencesUtil.roleTag) {
      roleTag = role;
    }

    String phone = await SharedPreferencesUtil.getData<String>(
        SharedPreferencesUtil.tel);
    if (phone != null && phone != "" && phone != SharedPreferencesUtil.tel) {
      tel = phone;
    }
  }

  static checkNoTokenJump() {
    if (token.isEmpty) {
      Get.to(() => LoginPage());
      return true;
    }
    return false;
  }
  // 判断是否登录进行页面跳转 1 登录页 2 交付中心消息页 3 首页
  static int checkNoTokenJumpTo()  {
    // 若为交付中心角色，跳转至消息页，若为其他角色，则跳转至BasePage
    if (token.isEmpty) {
      return 1;
    } else if (roleTag == "DeliveryCenter"||roleTag=="LearnTube") {
      return 2;
    } else if (roleTag == "DeliverTeamLeader") {
      return 3;
    }
    return 4;
  }

  /// 跳转登录页面
  static toLoginPage({bool isTime = false}) {
    if (isTime) {
      Timer(Duration(milliseconds: 2000), () {
        Get.to(() => LoginPage());
      });
    } else {
      Get.to(LoginPage());
    }
  }
}
