package com.dxznjy.deliver;

import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDelegate;

import com.dxznjy.deliver.methodchannel.PayMethodchannel;
import com.dxznjy.deliver.methodchannel.UniappMethodchannel;
import com.dxznjy.deliver.methodchannel.WgtHotUpdateChannel;
import com.qiyukf.unicorn.api.Unicorn;

import java.util.concurrent.TimeUnit;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

public class MainActivity extends FlutterActivity {
    Disposable mDisposable;
    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        flutterEngine.getPlugins().add(new UniappMethodchannel());
        flutterEngine.getPlugins().add(new PayMethodchannel());
        flutterEngine.getPlugins().add(new WgtHotUpdateChannel());
//        flutterEngine.getPlugins().add(new UserMethodchannel());
        DXApplication.app.flutterEngineInstance=flutterEngine;
//        GeneratedPluginRegistrant.registerWith(flutterEngine);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Unicorn.initSdk();
//        openShare();

    }


    public void openShare(){
        Uri uri = getIntent().getData();
        if (uri != null) {
            mDisposable = Observable.intervalRange(0, 10, 0, 1, TimeUnit.SECONDS)
                    .observeOn(AndroidSchedulers.mainThread())
                    .doOnNext(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) {
                            Log.i("zgj", aLong + "");
                        }
                    })
                    .doOnComplete(new Action() {
                        @Override
                        public void run() throws Exception {
                            String query = uri.getQuery();
                            Log.i("fxiaoli","quer12y------"+query);
                            //获取分享指定参数值
                            String methodChannelName="androidIosBackMeeting";
                            String json = "{\"path\":\"" + query+ "\"}";
                            MethodChannel methodChannel=new MethodChannel(DXApplication.app.flutterEngineInstance.getDartExecutor().getBinaryMessenger(),methodChannelName);
                            methodChannel.invokeMethod("navigateToFlutterPage",json);
                        }
                    })
                    .subscribe();
        }
    }
}