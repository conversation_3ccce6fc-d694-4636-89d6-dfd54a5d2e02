//
//  WgtHotUpdateChannel.swift
//  Runner
//
//  Created by AI Assistant on 2025/01/08.
//

import Foundation
import Flutter

/// wgt包热更新方法通道
class WgtHotUpdateChannel: NSObject, FlutterPlugin {
    private static let channelName = "wgt_hot_update"
    private var channel: FlutterMethodChannel?
    
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: channelName, binaryMessenger: registrar.messenger())
        let instance = WgtHotUpdateChannel()
        instance.channel = channel
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "getCurrentVersion":
            getCurrentVersion(call: call, result: result)
        case "installWgt":
            installWgt(call: call, result: result)
        case "downloadWgt":
            downloadWgt(call: call, result: result)
        case "checkWgtExists":
            checkWgtExists(call: call, result: result)
        case "removeWgt":
            removeWgt(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    /// 获取当前wgt版本
    private func getCurrentVersion(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let appId = args["appId"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "appId不能为空", details: nil))
            return
        }
        
        // 先从UserDefaults获取版本
        let userDefaults = UserDefaults.standard
        let key = "wgt_version_\(appId)"
        
        if let version = userDefaults.string(forKey: key) {
            result(version)
            return
        }
        
        // 如果UserDefaults中没有，尝试从uni-app SDK获取
        do {
            if DCUniMPSDKEngine.isExistsUniMP(appId) {
                if let versionInfo = DCUniMPSDKEngine.getUniMPVersionInfo(withAppid: appId) {
                    let version = versionInfo["code"] as? String ?? "1.0.0"
                    // 保存到UserDefaults
                    userDefaults.set(version, forKey: key)
                    result(version)
                } else {
                    let defaultVersion = "1.0.0"
                    userDefaults.set(defaultVersion, forKey: key)
                    result(defaultVersion)
                }
            } else {
                let defaultVersion = "1.0.0"
                userDefaults.set(defaultVersion, forKey: key)
                result(defaultVersion)
            }
        } catch {
            print("获取uni-app版本失败: \(error)")
            result("1.0.0")
        }
    }
    
    /// 安装wgt包
    private func installWgt(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let appId = args["appId"] as? String,
              let filePath = args["filePath"] as? String,
              let version = args["version"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "参数不能为空", details: nil))
            return
        }
        
        DispatchQueue.global(qos: .background).async {
            do {
                let fileURL = URL(fileURLWithPath: filePath)
                
                // 检查文件是否存在
                guard FileManager.default.fileExists(atPath: filePath) else {
                    DispatchQueue.main.async {
                        result(FlutterError(code: "FILE_NOT_EXISTS", message: "wgt文件不存在", details: nil))
                    }
                    return
                }
                
                // 如果已存在，先移除
                if DCUniMPSDKEngine.isExistsUniMP(appId) {
                    DCUniMPSDKEngine.closeUniMP()
                    _ = DCUniMPSDKEngine.releaseUniMP(withAppid: appId)
                }
                
                // 安装新的wgt包
                try DCUniMPSDKEngine.installUniMPResource(withAppid: appId, resourceFilePath: filePath, password: nil)
                
                // 保存版本信息到UserDefaults
                let userDefaults = UserDefaults.standard
                let key = "wgt_version_\(appId)"
                userDefaults.set(version, forKey: key)
                
                print("wgt包安装成功: \(appId) -> \(version)")
                
                DispatchQueue.main.async {
                    result(true)
                }
            } catch {
                print("安装wgt包失败: \(error)")
                DispatchQueue.main.async {
                    result(FlutterError(code: "INSTALL_ERROR", message: error.localizedDescription, details: nil))
                }
            }
        }
    }
    
    /// 下载wgt包
    private func downloadWgt(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let urlString = args["url"] as? String,
              let savePath = args["savePath"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "参数不能为空", details: nil))
            return
        }
        
        guard let url = URL(string: urlString) else {
            result(FlutterError(code: "INVALID_URL", message: "无效的URL", details: nil))
            return
        }
        
        DispatchQueue.global(qos: .background).async {
            do {
                let data = try Data(contentsOf: url)
                let saveURL = URL(fileURLWithPath: savePath)
                
                // 创建目录
                let directory = saveURL.deletingLastPathComponent()
                try FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true, attributes: nil)
                
                // 写入文件
                try data.write(to: saveURL)
                
                print("文件下载成功: \(savePath)")
                DispatchQueue.main.async {
                    result(true)
                }
            } catch {
                print("下载文件失败: \(error)")
                DispatchQueue.main.async {
                    result(FlutterError(code: "DOWNLOAD_ERROR", message: error.localizedDescription, details: nil))
                }
            }
        }
    }
    
    /// 检查wgt包是否存在
    private func checkWgtExists(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let appId = args["appId"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "appId不能为空", details: nil))
            return
        }
        
        let exists = DCUniMPSDKEngine.isExistsUniMP(appId)
        result(exists)
    }
    
    /// 移除wgt包
    private func removeWgt(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let appId = args["appId"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "appId不能为空", details: nil))
            return
        }
        
        do {
            // 关闭小程序
            DCUniMPSDKEngine.closeUniMP()
            
            // 移除小程序
            let success = DCUniMPSDKEngine.releaseUniMP(withAppid: appId)
            
            if success {
                // 清除版本信息
                let userDefaults = UserDefaults.standard
                let key = "wgt_version_\(appId)"
                userDefaults.removeObject(forKey: key)
            }
            
            result(success)
        } catch {
            print("移除wgt包失败: \(error)")
            result(FlutterError(code: "REMOVE_ERROR", message: error.localizedDescription, details: nil))
        }
    }
}

/// 扩展UrlProtocolResolver以支持热更新
extension UrlProtocolResolver {
    
    /// 检查并安装热更新包
    static func checkAndInstallHotUpdate(appId: String) {
        // 这里可以添加自动检查热更新的逻辑
        // 比如在应用启动时检查是否有新的wgt包需要安装
        print("检查热更新: \(appId)")
    }
    
    /// 获取wgt包版本信息
    static func getWgtVersionInfo(appId: String) -> [String: Any]? {
        if DCUniMPSDKEngine.isExistsUniMP(appId) {
            return DCUniMPSDKEngine.getUniMPVersionInfo(withAppid: appId)
        }
        return nil
    }
}
