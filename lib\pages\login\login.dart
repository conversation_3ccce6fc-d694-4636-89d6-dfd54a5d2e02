import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import '../../components/input_have_icon.dart';
import '../../components/toast_utils.dart';
import '../../res/colors.dart';
import 'controllers/login_controller.dart';
import './registe.dart';
import '../../utils/config_service.dart';

class LoginPage extends GetView<LoginController> {
  DateTime? _lastPressedTime;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    Get.put(LoginController());
    return WillPopScope(
        onWillPop: _onWillPop,
        child: Scaffold(
            backgroundColor: AppColors.white,
            resizeToAvoidBottomInset: false,
            body: GestureDetector(
              onTap: () {
                // 移除焦点
                FocusScope.of(context).unfocus();
              },
              child: Container(
                width: 750.w,
                height: screenSize.height > 2 * 750.w
                    ? screenSize.height
                    : 2 * 750.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/login/login-banner.png'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Obx(
                  () => Column(
                    children: [
                      getTitleUI(),
                      Expanded(child: showUIFromShowType()),
                    ],
                  ),
                ),
              ),
            )));
  }

  getTitleUI() {
    return Container(
      height: 120.h,
      width: double.infinity,
      padding: EdgeInsets.only(bottom: 20.h),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Text(getTitleName(),
              style: TextStyle(
                  color: AppColors.f333333, fontSize: 18.sp, fontFamily: "M"))
        ],
      ),
    );
  }

  getTitleName() {
    if (controller.showType.value == 1) {
      return "手机号验证码登录";
    } else if (controller.showType.value == 2) {
      return "登录";
    }
    return "登录";
  }

  showUIFromShowType() {
    if (controller.showType.value == 1) {
      return _smsUI();
    } else if (controller.showType.value == 2) {
      return _pwdUI();
    }
    return _loginBtnUI();
  }

  ///密码登录
  _pwdUI() {
    return SingleChildScrollView(
        child: Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      padding: EdgeInsets.only(top: 45.h, bottom: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.elliptical(10, 10), // 水平半径 20，垂直半径 10
        ),
      ),
      child: Column(
        children: [
          Image(
            image: AssetImage('assets/login/logo1.png'),
            fit: BoxFit.cover,
            width: 92.w,
            height: 88.w,
          ),
          Column(
            children: [
              InputHaveIcon().inputLeftHaveIcon(
                  height: 45.h,
                  top: 45.h,
                  left: 20.w,
                  right: 20.w,
                  radius: 8.w,
                  hintText: '请输入手机号',
                  radiusColor: AppColors.ececea,
                  keyboardType: TextInputType.phone,
                  controller: controller.phoneController,
                  fillColor: AppColors.c8c8c8,
                  cursorColor: AppColors.c8c8c8,
                  hintColor: AppColors.c8c8c8,
                  labelColor: AppColors.f333333,
                  fontSize: 14.sp,
                  img: "assets/login/icon_login_user.png",
                  maxLength: 11,
                  imgH: 18.h,
                  imgW: 18.w),
              InputHaveIcon().inputLeftHaveIcon(
                  height: 45.h,
                  top: 25.h,
                  left: 20.w,
                  right: 20.w,
                  radius: 8.w,
                  hintText: '请输入密码',
                  radiusColor: AppColors.ececea,
                  keyboardType: TextInputType.visiblePassword,
                  controller: controller.pwdController,
                  fillColor: AppColors.c8c8c8,
                  cursorColor: AppColors.c8c8c8,
                  hintColor: AppColors.c8c8c8,
                  labelColor: AppColors.f333333,
                  fontSize: 14.sp,
                  img: "assets/login/icon_login_ped.png",
                  imgH: 18.h,
                  imgW: 18.w,
                  isPwd: true),
              SizedBox(
                height: 10.h,
              ),
              GestureDetector(
                child: Container(
                  width: 280.w,
                  height: 34.h,
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(top: 20.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f88cfba, AppColors.f1d755c],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    controller.isDisable ? '登录中···' : '登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.pwdLoginFun(
                      controller.phoneController.text,
                      controller.pwdController.text,
                      controller.isChecked.value);
                },
              ),
              Visibility(
                  visible: ConfigService.showDjjRegiste,
                  child: Container(
                    margin: EdgeInsets.only(top: 10.h),
                    child: GestureDetector(
                      child: Center(
                        child: Text(
                          '注册',
                          style: TextStyle(
                              color: AppColors.f555555, fontSize: 14.sp),
                        ),
                      ),
                      onTap: () {
                        // RegistePage
                        Get.to(() => RegistePage());
                      },
                    ),
                  )),
              _checkUI(),
            ],
          )
        ],
      ),
    ));
  }

  ///验证码登录
  _smsUI() {
    return SingleChildScrollView(
        child: Column(
      children: [
        Image(
          image: AssetImage('assets/login/login_center.png'),
          fit: BoxFit.cover,
          width: 312.w,
          height: 308.h,
        ),
        Column(
          children: [
            InputHaveIcon().inputLeftHaveIcon(
                height: 38.h,
                top: 30.h,
                left: 30.w,
                right: 30.w,
                radius: 40.0,
                hintText: '请输入手机号',
                radiusColor: AppColors.ececea,
                keyboardType: TextInputType.phone,
                controller: controller.phoneController,
                fillColor: AppColors.c8c8c8,
                cursorColor: AppColors.c8c8c8,
                hintColor: AppColors.c8c8c8,
                labelColor: AppColors.f333333,
                fontSize: 14.sp,
                img: "assets/login/icon_login_user.png",
                imgH: 18.h,
                imgW: 18.w),
            Container(
              height: 38.h,
              margin: EdgeInsets.only(top: 20.h, left: 30.w, right: 30.w),
              padding: EdgeInsets.only(left: 15.w, right: 10.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(color: AppColors.ececea, width: 1),
                  color: Colors.white),
              child: Row(
                children: [
                  Image(
                    image: AssetImage("assets/login/icon_login_code.png"),
                    height: 18.h,
                    width: 18.w,
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 10.w, right: 10.w),
                    height: double.infinity,
                    width: 150.w,
                    child: TextField(
                      focusNode: controller.focusNode,
                      controller: controller.codeController,
                      cursorColor: AppColors.c8c8c8,
                      keyboardType: TextInputType.number,
                      onSubmitted: (String text) {},
                      decoration: InputDecoration(
                        hintText: "请输入验证码",
                        border: InputBorder.none,
                        fillColor: AppColors.c8c8c8,
                        hintStyle: TextStyle(
                            fontFamily: "R",
                            color: AppColors.c8c8c8,
                            fontSize: 14.sp),
                        labelStyle: TextStyle(
                          fontFamily: "R",
                          color: AppColors.f333333,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 20.h,
                    width: 1,
                    decoration: BoxDecoration(
                      color: AppColors.c8c8c8,
                    ),
                  ),
                  Expanded(
                      child: GestureDetector(
                    onTap: () {
                      controller.checkSlideOption();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        controller.timerText.value,
                        style: TextStyle(
                            color: AppColors.ea6531,
                            fontSize: 12.sp,
                            fontFamily: "R"),
                      ),
                    ),
                  ))
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            _checkUI(),
            GestureDetector(
              child: Container(
                width: 280.w,
                height: 34.h,
                alignment: AlignmentDirectional.center,
                margin: EdgeInsets.only(top: 20.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [AppColors.f88cfba, AppColors.f1d755c],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter),
                  borderRadius: BorderRadius.circular(23),
                ),
                child: Text(
                  '登录',
                  style: TextStyle(
                      color: AppColors.white, fontSize: 14.sp, fontFamily: 'R'),
                ),
              ),
              onTap: () {
                controller.smsLoginFun();
              },
            ),
          ],
        )
      ],
    ));
  }

  _checkUI() {
    return GestureDetector(
      onTap: () {
        controller.changeCheck();
      },
      child: Container(
        alignment: Alignment.center,
        height: 20.h,
        width: double.infinity,
        margin: EdgeInsets.only(top: 60.h),
        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          Image(
              width: 15.w,
              height: 15.w,
              image: AssetImage(controller.isChecked.value
                  ? "assets/login/icon_chose.png"
                  : "assets/login/icon_normal.png")),
          SizedBox(
            width: 5.w,
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(color: AppColors.c8c8c8, fontSize: 13.sp),
              children: <TextSpan>[
                TextSpan(text: '我已阅读并同意'),
                TextSpan(
                  text: '《用户服务协议》',
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      controller.clickUserAgree();
                    },
                ),
                TextSpan(text: '、'),
                TextSpan(
                  text: '《隐私政策》',
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      controller.clickPrivacy();
                    },
                ),
              ],
            ),
          )
        ]),
      ),
    );
  }

  _loginBtnUI() {
    return Column(
      children: [
        Image(
          image: AssetImage('assets/login/login_center.png'),
          fit: BoxFit.cover,
          width: 312.w,
          height: 308.h,
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //手机验证码登录
              GestureDetector(
                child: Container(
                  width: 240.w,
                  height: 32.h,
                  alignment: AlignmentDirectional.center,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f08c34, AppColors.ea6531],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    '手机号验证码登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.cleanData();
                  controller.showType.value = 1;
                },
              ),
              //用户名密码登录
              GestureDetector(
                child: Container(
                  width: 240.w,
                  height: 32.h,
                  alignment: AlignmentDirectional.center,
                  margin: EdgeInsets.only(top: 15.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [AppColors.f78d2aa, AppColors.f368e5c],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter),
                    borderRadius: BorderRadius.circular(23),
                  ),
                  child: Text(
                    '用户名密码登录',
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14.sp,
                        fontFamily: 'R'),
                  ),
                ),
                onTap: () {
                  controller.cleanData();
                  controller.showType.value = 2;
                },
              ),
              SizedBox(
                height: 20.h,
              ),
            ],
          ),
        )
      ],
    );
  }

  Future<bool> _onWillPop() async {
    if (Platform.isIOS) {
      return false;
    }
    final now = DateTime.now();
    if (_lastPressedTime == null ||
        now.difference(_lastPressedTime!) > Duration(seconds: 2)) {
      _lastPressedTime = now;
      ToastUtil.showToastText("再按一次退出应用");
      return false;
    }
    SystemNavigator.pop();
    return true;
  }
}
