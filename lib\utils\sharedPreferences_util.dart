import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesUtil {
  static final String tokenKey = "TOKEN_KEY";
  static final String token = "TOKEN";
  static final String roleTag = "ROLE_TAG";
  static final String isAppRoleValue = 'isAppRoleValue'; //DeliverTeamLeader 组长
  static final String tel = 'tel';
  static final String agreeTips = "AGREE_TIPS";//同意权限

  /// 保存数据
  static saveData<T>(String key, T value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    switch (T) {
      case String:
        prefs.setString(key, value as String);
        break;
      case int:
        prefs.setInt(key, value as int);
        break;
      case bool:
        prefs.setBool(key, value as bool);
        break;
      case double:
        prefs.setDouble(key, value as double);
        break;
    }
  }

  /// 读取数据
  static Future<T> getData<T>(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var res;
    switch (T) {
      case String:
        if (prefs.getString(key) == null) {
          res = "";
        } else {
          res = prefs.getString(key) as T;
        }
        break;
      case int:
        if (prefs.getInt(key) == null) {
          res = -1;
        } else {
          res = prefs.getInt(key) as T;
        }
        break;
      case bool:
        res = prefs.getBool(key) as T;
        break;
      case double:
        res = prefs.getDouble(key) as T;
        break;
    }
    return res;
  }

  /// 删除指定键的数据
  static Future<void> deleteData(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
}
