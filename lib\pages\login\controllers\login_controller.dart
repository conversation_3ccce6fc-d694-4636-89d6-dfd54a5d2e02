import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:student_end_flutter/common/user_option.dart';
import 'package:student_end_flutter/pages/base/group_leader_page.dart';
import 'package:tx_im/im_home_page.dart';
import 'package:tx_im/dx_utils/IMHttpRequest.dart';
import 'package:tx_im/im_module_service.dart';
import 'package:tx_im/user.dart';
import '../../../common/Config.dart';
import '../../../common/user_option.dart';
import '../../../components/sms_verify_widget.dart';
import '../../../components/toast_utils.dart';
import '../../../utils/android_ios_plugin.dart';
import '../../../utils/httpRequest.dart';
import '../../../utils/utils.dart';
import '../../../utils/sharedPreferences_util.dart';
import '../../../dialog/login_privacy_dialog.dart';
import 'package:student_end_flutter/pages/base/base_page.dart';
import 'package:flutter/material.dart';
import '../webView_page.dart';
import '../../base/controller/base_controller.dart';
import '../login.dart';
//user_option

class LoginController extends GetxController {
  // 获取当前的 BuildContext
  BuildContext get context => Get.context!;

  final TextEditingController phoneController = TextEditingController();
  final TextEditingController pwdController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController registeController = TextEditingController();
  final TextEditingController registePwdController = TextEditingController();

  RxInt showType = 2.obs;

  ///0按钮  1验证码  2密码

  RxBool isChecked = false.obs;
  bool isDisable = false;
  bool isRegiste = false;

  Timer? timer;
  RxString timerText = "发送验证码".obs;
  RxInt codeTime = 60.obs;
  FocusNode focusNode = FocusNode();
  static String memberKey = "";
  String memberToken = "";
  // 设置焦点
  void setFocus() {
    FocusScope.of(context).requestFocus(focusNode);
  }

  // 清除焦点
  void removeFocus() {
    focusNode.unfocus();
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    cancelTimer();
    focusNode.dispose();
    super.onClose();
  }

  cancelTimer() {
    timer?.cancel();
  }

  changeCheck() {
    isChecked.value = !isChecked.value;
  }

  clickUserAgree() {
    Get.to(() => WebViewPage(), arguments: {
      "titleName": "用户服务协议",
      "url": "https://document.dxznjy.com/applet/agreeon/useragreement.html"
    });
  }

  clickPrivacy() {
    Get.to(() => WebViewPage(), arguments: {
      "titleName": "隐私政策",
      "url": "https://document.dxznjy.com/applet/agreeon/djj_privacypolicy.html"
    });
  }

  //////////////////////账号注册////////////////////
  pwdRegiste(String phone, String pwd, bool isChecked) async {
    if (phone.length == 0) {
      ToastUtil.showShortErrorToast("请输入手机号");
      return;
    }
    if (pwd.length == 0) {
      ToastUtil.showShortErrorToast("请输入密码");
      return;
    }
    if (phone.length != 11 || !Utils.checkPhone(phone)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    if (isDisable) {
      ToastUtil.showToastText("请勿重复点击");
      return;
    }
    isDisable = true;
    var responseinfo = await HttpUtil()
        .put("${Config.deliverRegister}?mobile=$phone&password=$pwd");
    isDisable = false;
    if (responseinfo != null && responseinfo.data != null) {
      ToastUtil.showShortSuccessToast("注册成功");
      cleanData();
      Get.off(() => LoginPage());
    }
  }

  //////////////////////密码登录////////////////////
  pwdLoginFun(
      String phoneController, String pwdController, bool isChecked) async {
    if (phoneController.length == 0) {
      ToastUtil.showShortErrorToast("请输入手机号");
      return;
    }
    if (pwdController.length == 0) {
      ToastUtil.showShortErrorToast("请输入密码");
      return;
    }
    if (phoneController.length != 11 || !Utils.checkPhone(phoneController)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    if (!isChecked) {
      var result = await showJudgeDialog();
      FocusScope.of(context).unfocus(); // 取消焦点
      if (result) {
        login();
      }
      return;
    }
    if (isDisable) {
      ToastUtil.showToastText("请勿重复点击");
      return;
    }
    isDisable = true;
    ToastUtil.showLoadingDialog();
    var response = await HttpUtil().get(
        "${Config.deliverLogin}?username=$phoneController&password=$pwdController");
    isDisable = false;
    ToastUtil.closeLoadingDialog();
    memberKey = response['tokenType'];
    memberToken = response['token'];
    await SharedPreferencesUtil.saveData(
        SharedPreferencesUtil.tel, phoneController);
    await SharedPreferencesUtil.saveData(
        SharedPreferencesUtil.tokenKey, memberKey);
    await SharedPreferencesUtil.saveData(
        SharedPreferencesUtil.token, memberToken);
    UserOption.initToken().then((_) {
      IMModuleService().mobile = UserOption.tel;
    });
    HttpUtil.heads.remove(memberKey);
    HttpUtil.heads.putIfAbsent(memberKey, () => memberToken);
    //token 保持一致
    IMHttpUtil.heads = HttpUtil.heads;
    IMHttpUtil().options?.headers = HttpUtil.heads;
    // 登录成功后获取登录角色(交付中心---DeliveryCenter)

    var data = {'app': 'DELIVER'};
    var userRole = await HttpUtil().get(Config.getUserRoleInfo,data: data);

    SharedPreferencesUtil.saveData(SharedPreferencesUtil.roleTag, userRole.toString());
    // 根据角色登录跳转不同页面，交付中心角色跳转IM消息页
    FocusScope.of(context).unfocus(); // 取消焦点
    await IMModuleService().TIMLogin();
    await getTeacherInfo();
    if (userRole == "DeliveryCenter"||userRole=="LearnTube") {
      Get.off(() => IMModuleHomePage());
    } else if (userRole == "DeliverTeamLeader") {
      Get.off(() => GroupLeaderPage());
    } else {
      Get.off(() => BasePage());
    }
  }

  login() {
    isChecked.value = true;
    pwdLoginFun(phoneController.text, pwdController.text, isChecked.value);
  }

  showJudgeDialog() async {
    var reportContent = await showDialog<bool>(
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            elevation: 0,
            backgroundColor: Color.fromRGBO(0, 0, 0, 0),
            child: ChoseModelJudgeDialog(() => {login()}),
          );
        },
        context: context);
    return reportContent!;
  }

  getTeacherInfo() async {
    var responseInfo = await HttpUtil().get(Config.getAppUserInfo);
    if(responseInfo['data']=='visitor'){
      SharedPreferencesUtil.saveData(SharedPreferencesUtil.isAppRoleValue,
          'visitor');
    }else{
      SharedPreferencesUtil.saveData(SharedPreferencesUtil.isAppRoleValue,
          responseInfo['appRoleValue'].toString());
    }

    cleanData();
    isDisable = false;
  }

  /// 切换时清除数据
  cleanData() {
    phoneController.text = "";
    pwdController.text = "";
    codeController.text = "";
    registeController.text="";
    registePwdController.text="";
    isChecked.value = false;
    isDisable = false;
    cancelTimer();
    timerText.value = "发送验证码";
    codeTime.value = 60;
  }

  //////////////////////验证码登录////////////////////
  checkSlideOption() {
    if (phoneController.text.length != 11 ||
        !Utils.checkPhone(phoneController.text)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    if (timerText.value != "发送验证码") {
      ToastUtil.showShortErrorToast("验证码已发送，请注意查收");
      return;
    }
    setFocus();
    Get.dialog(
      BlockPuzzleCaptchaPage(sendSms),
      barrierDismissible: false,
    );
  }

  sendSms(code, uuid) {
    if (phoneController.text.length != 11 ||
        !Utils.checkPhone(phoneController.text)) {
      ToastUtil.showShortErrorToast("请输入正确的手机号");
      return;
    }
    String params = "?mobile=${phoneController.text}";
    var response = HttpUtil().post('${Config.checkRegister}$params');
    response.then((value) async {
      if (value?.data["status"] == 1) {
        var response2 = HttpUtil().post(
            '${Config.sendSms}/${phoneController.text}?code=$code&uuid=$uuid');
        response2.then((data) async {
          if (data?.data["success"]) {
            ToastUtil.showShortSuccessToast("发送验证码成功");
            timerFunc();
          } else {
            ToastUtil.showShortErrorToast("发送验证码失败");
          }
        });
      } else {
        ToastUtil.showShortErrorToast(value?.data["message"] ?? "发送验证码失败");
      }
    });
  }

  timerFunc() {
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      codeTime.value--;
      timerText.value = "${codeTime.value}s秒后重发";
      if (codeTime.value == 0) {
        cancelTimer();
        codeTime.value = 60;
        timerText.value = "发送验证码";
      }
    });
  }

  smsLoginFun() {
    if (isDisable) {
      ToastUtil.showToastText("请勿重复点击");
      return;
    }
    isDisable = true;
  }
}
