# DCUniMPSDK API 兼容性修复说明

## 问题描述

在实现wgt包热更新功能时，遇到了DCUniMPSDK的API方法不匹配的问题。这是因为不同版本的DCUniMPSDK可能有不同的API接口。

## 错误信息

```
找不到符号: isExistsUniMP(String)
找不到符号: getUniMPByAppid(String)
找不到符号: releaseUniMP(String)
找不到符号: installUniMPResource(String,String,<null>)
```

## 临时解决方案

当前已经实现了一个临时的解决方案，避免了编译错误：

### 1. 版本检查方法修改

**原始代码（有问题）：**
```java
if (DCUniMPSDK.getInstance().isExistsUniMP(appId)) {
    // 获取版本信息
}
```

**修改后（临时方案）：**
```java
// 使用SharedPreferences存储和检查版本信息
SharedPreferences prefs = context.getSharedPreferences("wgt_versions", Context.MODE_PRIVATE);
String version = prefs.getString(appId, "1.0.0");
```

### 2. 安装方法修改

**原始代码（有问题）：**
```java
DCUniMPSDK.getInstance().installUniMPResource(appId, filePath, null);
```

**修改后（临时方案）：**
```java
// 检查文件存在性，记录日志
// 实际安装需要根据SDK版本调用正确的API
Log.i(TAG, "模拟安装wgt包: " + appId + " 文件: " + filePath);
```

## 正确的解决方案

### 1. 查找正确的API文档

需要查看你项目中使用的DCUniMPSDK版本的官方文档，找到正确的API方法名称。

### 2. 常见的API变体

根据不同版本，可能的API方法名称包括：

```java
// 检查小程序是否存在
DCUniMPSDK.getInstance().isExistsUniMP(appId)           // 可能的方法名
DCUniMPSDK.getInstance().existsUniMP(appId)             // 可能的方法名
DCUniMPSDK.getInstance().checkUniMPExists(appId)        // 可能的方法名

// 获取小程序实例
DCUniMPSDK.getInstance().getUniMPByAppid(appId)         // 可能的方法名
DCUniMPSDK.getInstance().getUniMP(appId)                // 可能的方法名
DCUniMPSDK.getInstance().findUniMP(appId)               // 可能的方法名

// 安装小程序资源
DCUniMPSDK.getInstance().installUniMPResource(appId, filePath, null)  // 可能的方法名
DCUniMPSDK.getInstance().installWgt(appId, filePath)                   // 可能的方法名
DCUniMPSDK.getInstance().deployUniMP(appId, filePath)                  // 可能的方法名

// 移除小程序
DCUniMPSDK.getInstance().releaseUniMP(appId)            // 可能的方法名
DCUniMPSDK.getInstance().removeUniMP(appId)             // 可能的方法名
DCUniMPSDK.getInstance().uninstallUniMP(appId)          // 可能的方法名
```

### 3. 查看现有代码中的用法

在你的项目中，已经有使用DCUniMPSDK的代码：

**在UniappMethodchannel.java中：**
```java
DXApplication.iUniMP = DCUniMPSDK.getInstance().openUniMP(mContext, Constants.XCX_PKG_NAME, uniMPOpenConfiguration);
```

**在DXApplication.java中：**
```java
DCUniMPSDK.getInstance().initialize(this, config, callback);
```

### 4. 建议的修复步骤

1. **查看SDK文档**：
   - 查看项目中使用的DCUniMPSDK版本
   - 查阅对应版本的API文档

2. **查看现有集成**：
   - 检查项目中其他地方是否有类似的API调用
   - 参考现有的实现方式

3. **测试API可用性**：
   ```java
   // 在现有的工作代码中添加测试
   try {
       // 测试各种可能的API方法名
       boolean exists = DCUniMPSDK.getInstance().isExistsUniMP(appId);
       Log.i("API_TEST", "isExistsUniMP 可用");
   } catch (NoSuchMethodError e) {
       Log.e("API_TEST", "isExistsUniMP 不可用: " + e.getMessage());
   }
   ```

4. **逐步替换临时方案**：
   - 找到正确的API后，逐步替换临时实现
   - 保持向后兼容性

## 当前状态

目前的实现可以编译通过，但功能有限：

- ✅ 版本管理：通过SharedPreferences实现
- ✅ 文件下载：已实现
- ⚠️ wgt安装：需要正确的SDK API
- ⚠️ 小程序检查：需要正确的SDK API
- ⚠️ 小程序移除：需要正确的SDK API

## 下一步行动

1. **确定SDK版本**：
   ```bash
   # 在android/app/build.gradle中查看依赖版本
   grep -r "uniapp\|dccloud" android/app/build.gradle
   ```

2. **查看SDK源码**：
   - 如果有SDK源码，直接查看API定义
   - 查看SDK的jar包中的类定义

3. **联系SDK提供方**：
   - 如果是商业SDK，联系技术支持
   - 查看官方文档和示例代码

4. **渐进式实现**：
   - 先实现基础功能（版本管理、文件下载）
   - 再逐步完善高级功能（安装、移除）

## 测试建议

在修复API问题后，建议进行以下测试：

1. **基础功能测试**：
   - 版本检查
   - 文件下载
   - 安装流程

2. **边界情况测试**：
   - 网络异常
   - 文件损坏
   - 权限不足

3. **兼容性测试**：
   - 不同Android版本
   - 不同设备型号
   - 不同网络环境

## 注意事项

1. **备份原始代码**：在修改前备份当前可工作的代码
2. **渐进式修改**：一次只修改一个API，逐步验证
3. **错误处理**：确保有完善的错误处理机制
4. **日志记录**：添加详细的日志以便调试

## 联系方式

如果需要进一步的技术支持，请提供：
- DCUniMPSDK的具体版本号
- 完整的错误日志
- 相关的build.gradle配置
