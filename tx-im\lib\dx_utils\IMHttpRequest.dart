import 'dart:collection';
import 'dart:convert';
import 'package:bot_toast/bot_toast.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:tuple/tuple.dart';
import 'package:tx_im/config.dart';
import 'package:tx_im/dx_utils/http_config.dart';
import '../model/base_bean/base_bean_entity.dart';

class IMHttpUtil {
  static IMHttpUtil? instance;
  late Dio dio;
  BaseOptions? options;
  CancelToken cancelToken = CancelToken();
  static LinkedHashMap<String, dynamic> heads = LinkedHashMap();
  late String teachModel;

  // 单例模式获取HttpUtil实例
  static IMHttpUtil? getInstance() {
    instance ??= IMHttpUtil();
    return instance;
  }

  IMHttpUtil() {
    options = BaseOptions(
      baseUrl: HttpConfig.URL,
      connectTimeout: const Duration(milliseconds: 120000),
      receiveTimeout: const Duration(milliseconds: 60000),
      headers: heads,
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );

    dio = Dio(options);

    // 忽略证书过期报错
    (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) => true;
    };
  }

  // 判断是否有网络连接
  static Future<bool> isNetworkAvailable() async {
    try {
      List<ConnectivityResult> resultList = await Connectivity().checkConnectivity();
      ConnectivityResult result = resultList.isNotEmpty ? resultList[0] : ConnectivityResult.none;
      // 如果是手机数据网络或者 Wi-Fi 网络
      if (result == ConnectivityResult.mobile || result == ConnectivityResult.wifi) {
        return true;
      } else {
        // 如果没有网络连接
        // ToastUtil.showShortErrorToast("网络不可用，请检查网络连接");
        return false;
      }
    } catch (e) {
      // ToastUtil.showShortErrorToast("网络检查失败，请稍后再试");
      return false;
    }
  }

  // 公共的请求错误处理方法
  void _handleError(DioError e, String url) {
    print("==errUrl===$url ${e.message}");
    String errorMessage = '';
    if (e.response != null && e.response?.statusCode == 401) {
      errorMessage = '登录过期，请重新登录';
    } else if (e.type == DioErrorType.connectionTimeout) {
      errorMessage = '连接超时，请刷新重试';
    } else if (e.type == DioErrorType.sendTimeout) {
      errorMessage = '请求超时，请刷新重试';
    } else if (e.type == DioErrorType.receiveTimeout) {
      errorMessage = '响应超时，请刷新重试';
    } else if (e.type == DioErrorType.badResponse) {
      errorMessage = '服务器异常，请刷新重试';
    } else if (e.type == DioErrorType.cancel) {
      errorMessage = '请求取消';
    } else {
      errorMessage = '未知错误，请刷新重试';
    }
    BotToast.closeAllLoading();
    BotToast.showText(text: errorMessage);
  }


  // 公共方法：处理响应数据
  static tryResponse(Response? response) {
    if (response == null) {
      return;
    }
    if (response.data == null) {
      response.data = {};
    }
  }

  // 请求数据是否成功
  bool _isRequestSuccessful(Response? response) {
    if (response == null || response.data == null) {
      return false;
    }
    Map responseData = response.data;
    /// 兼容两种数据返回格式
    if (responseData.containsKey('code')) {
      if (response.data['code'] == null) {
        return false;
      }
      if (!response.data['code'].toString().startsWith("2")) {
        if (response.data['code'] == 40011) {
          // BotToast.showText(text: "${response.data['message'] ?? "请刷新重试"}");
          Future.delayed(const Duration(seconds: 2), () {
            // UserOption.toLoginPage();
          });
          return false;
        } else if (response.data['code'] == 40008 || response.statusCode == 401) {
          Future.delayed(const Duration(seconds: 2), () {
            // UserOption.toLoginPage();
          });
          return false;
        }
      }
    } else if (responseData.containsKey('status')) {
      if (response.data['status'] == null) {
        return false;
      }
      if (response.data['status'] != 1) {
          // BotToast.showText(text: "${response.data['message'] ?? "请刷新重试"}");
          Future.delayed(const Duration(seconds: 2), () {
            // UserOption.toLoginPage();
          });
          return false;
      }
    }
    return true;
  }

  // GET请求
  Future<Tuple2<bool,T?>?> get<T>(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      print("get-----------------$url-${json.encode(data)}");
      final response = await dio.get(url, queryParameters: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("get-----------------$url-${json.encode(data)}-$response");
      if (_isRequestSuccessful(response)) {
        if(url.contains("group/get-group-data") ) {
          return Tuple2(true, response.data);
        }else{
          BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
          if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
            return Tuple2(true, baseBeanEntity.data);
          } else{
            if (url.contains(HttpConfig.chatUserInfo) == false && baseBeanEntity.code != 41008 ) {
              BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");
            }
            return Tuple2(false, baseBeanEntity.message as T?);
          }
        }
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }

  // POST请求
  Future<Tuple2<bool,T?>?> post<T>(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken, bool noShowError = false}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;
    try {
      final response = await dio.post(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      print("post-----------------$url-${json.encode(data)}-$response");
      if (_isRequestSuccessful(response)) {
        BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
        if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
          return Tuple2(true, baseBeanEntity.data);
        }else{
          if (noShowError == false) {
            BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");
            return Tuple2(false, baseBeanEntity.message as T?);
          }
        }
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }

  // PUT请求
  Future<Response?> put(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.put(url, data: data, options: options, cancelToken: cancelToken);
      BotToast.closeAllLoading();
      if (_isRequestSuccessful(response)) {
        BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
        if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
          return baseBeanEntity.data;
        } else{
          BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");

        }
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }

  // DELETE请求
  Future<Response?> delete(String url, {Map<String, dynamic>? data, Options? options, CancelToken? cancelToken}) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.delete(url, queryParameters: data, options: options, cancelToken: cancelToken);
      print("--delete--response-$url$response");
      BotToast.closeAllLoading();
      if (_isRequestSuccessful(response)) {
        BaseBeanEntity? baseBeanEntity = BaseBeanEntity.fromJson(response.data);
        if (baseBeanEntity.code == 20000 || baseBeanEntity.status == 1 ) {
          return baseBeanEntity.data;
        }else{
          BotToast.showText(text: baseBeanEntity.message ?? "服务器返回失败");
        }
      }
    } on DioError catch (e) {
      _handleError(e, url);
    }
    return null;
  }

  // 下载文件
  Future downloadFile(String urlPath, String savePath) async {
    bool isConnected = await isNetworkAvailable();
    if (!isConnected) return null;

    try {
      final response = await dio.download(urlPath, savePath, onReceiveProgress: (count, total) {
        print("$count $total");
      });
      print('Download success: $response');
    } on DioError catch (e) {
      _handleError(e, urlPath);
    }
  }

  // 取消请求
  void cancelRequests(CancelToken token) {
    token.cancel("Request cancelled");
  }
}

//拼接url
class PathParam {
  static String paramToString(Map<String, String> map) {
    String url = '?';
    map.forEach((key, value) {
      url += '$key=$value&';
    });
    if (url.endsWith('&')) {
      url = url.substring(0, url.length - 1);
    }
    return url;
  }
}
