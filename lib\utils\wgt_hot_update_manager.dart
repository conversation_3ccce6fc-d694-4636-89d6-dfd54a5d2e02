import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:student_end_flutter/components/toast_utils.dart';
import 'package:student_end_flutter/utils/httpRequest.dart';
import 'package:student_end_flutter/common/Config.dart';
import 'package:student_end_flutter/common/wgt_api_config.dart';

/// wgt包热更新管理器
class WgtHotUpdateManager {
  static const String _methodChannelName = "wgt_hot_update";
  static const MethodChannel _methodChannel = MethodChannel(_methodChannelName);

  // 小程序包ID
  static const String _uniAppId = "__UNI__201304B";

  // SharedPreferences键名
  static const String _keyWgtVersion = "wgt_version_$_uniAppId";
  static const String _keyLastCheckTime = "last_check_time_$_uniAppId";

  // 检查间隔（毫秒）- 24小时
  static const int _checkInterval = 24 * 60 * 60 * 1000;

  /// 单例模式
  static final WgtHotUpdateManager _instance = WgtHotUpdateManager._internal();
  factory WgtHotUpdateManager() => _instance;
  WgtHotUpdateManager._internal();

  /// 检查wgt包更新
  /// [forceCheck] 是否强制检查，忽略时间间隔
  /// [showToast] 是否显示提示信息
  static Future<WgtUpdateInfo?> checkUpdate({
    bool forceCheck = false,
    bool showToast = true,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckTime = prefs.getInt(_keyLastCheckTime) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // 检查是否需要检查更新
      if (!forceCheck && (currentTime - lastCheckTime) < _checkInterval) {
        if (showToast) {
          ToastUtil.showToastText("距离上次检查时间过短，请稍后再试");
        }
        return null;
      }

      // 获取当前版本
      final currentVersion = await getCurrentWgtVersion();

      // 获取设备ID
      final deviceId = await _getDeviceId();

      // 请求服务器获取最新版本信息
      final url = WgtApiConfig.checkVersionUrl(
        appId: _uniAppId,
        currentVersion: currentVersion,
        platform: WgtApiConfig.getCurrentPlatform(),
        deviceId: deviceId,
      );

      final response = await HttpUtil().get(url);

      if (response == null) {
        if (showToast) {
          ToastUtil.showToastText("检查更新失败，请检查网络连接");
        }
        return null;
      }

      final updateInfo = WgtUpdateInfo.fromJson(response);

      // 更新最后检查时间
      await prefs.setInt(_keyLastCheckTime, currentTime);

      if (updateInfo.hasUpdate) {
        if (showToast) {
          ToastUtil.showToastText("发现新版本：${updateInfo.latestVersion}");
        }
        return updateInfo;
      } else {
        if (showToast) {
          ToastUtil.showToastText("当前已是最新版本");
        }
        return null;
      }
    } catch (e) {
      print("检查wgt更新失败: $e");
      if (showToast) {
        ToastUtil.showToastText("检查更新失败：$e");
      }
      return null;
    }
  }

  /// 下载并安装wgt包
  /// [updateInfo] 更新信息
  /// [onProgress] 下载进度回调
  static Future<bool> downloadAndInstall(
    WgtUpdateInfo updateInfo, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      // 获取下载目录
      final tempDir = await getTemporaryDirectory();
      final wgtFileName = "${_uniAppId}_${updateInfo.latestVersion}.wgt";
      final wgtFilePath = "${tempDir.path}/$wgtFileName";

      // 上报下载开始状态
      await reportUpdateStatus(
        version: updateInfo.latestVersion,
        status: WgtUpdateStatus.downloading,
      );

      // 下载wgt文件
      ToastUtil.showToastText("开始下载更新包...");

      final success = await _downloadWgtFile(
        updateInfo.downloadUrl,
        wgtFilePath,
        onProgress: onProgress,
      );

      if (!success) {
        ToastUtil.showToastText("下载失败");
        await reportUpdateStatus(
          version: updateInfo.latestVersion,
          status: WgtUpdateStatus.failed,
          errorMessage: "下载失败",
        );
        return false;
      }

      // 上报安装开始状态
      await reportUpdateStatus(
        version: updateInfo.latestVersion,
        status: WgtUpdateStatus.installing,
      );

      // 安装wgt包
      ToastUtil.showToastText("开始安装更新包...");
      final installSuccess =
          await _installWgtFile(wgtFilePath, updateInfo.latestVersion);

      if (installSuccess) {
        // 更新本地版本记录
        await _updateLocalVersion(updateInfo.latestVersion);
        ToastUtil.showToastText("更新安装成功");

        // 上报安装成功状态
        await reportUpdateStatus(
          version: updateInfo.latestVersion,
          status: WgtUpdateStatus.success,
        );

        // 删除临时文件
        final file = File(wgtFilePath);
        if (await file.exists()) {
          await file.delete();
        }

        return true;
      } else {
        ToastUtil.showToastText("安装失败");
        await reportUpdateStatus(
          version: updateInfo.latestVersion,
          status: WgtUpdateStatus.failed,
          errorMessage: "安装失败",
        );
        return false;
      }
    } catch (e) {
      print("下载安装wgt包失败: $e");
      ToastUtil.showToastText("更新失败：$e");
      return false;
    }
  }

  /// 获取当前wgt版本
  static Future<String> getCurrentWgtVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localVersion = prefs.getString(_keyWgtVersion);

      if (localVersion != null) {
        return localVersion;
      }

      // 从原生端获取版本信息
      final nativeVersion =
          await _methodChannel.invokeMethod('getCurrentVersion', {
        'appId': _uniAppId,
      });

      return nativeVersion?.toString() ?? "1.0.0";
    } catch (e) {
      print("获取当前wgt版本失败: $e");
      return "1.0.0";
    }
  }

  /// 下载wgt文件
  static Future<bool> _downloadWgtFile(
    String url,
    String savePath, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      await HttpUtil().downloadFile(url, savePath);
      return true;
    } catch (e) {
      print("下载wgt文件失败: $e");
      return false;
    }
  }

  /// 安装wgt文件
  static Future<bool> _installWgtFile(String filePath, String version) async {
    try {
      final result = await _methodChannel.invokeMethod('installWgt', {
        'appId': _uniAppId,
        'filePath': filePath,
        'version': version,
      });

      return result == true;
    } catch (e) {
      print("安装wgt文件失败: $e");
      return false;
    }
  }

  /// 更新本地版本记录
  static Future<void> _updateLocalVersion(String version) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyWgtVersion, version);
  }

  /// 清除本地版本缓存
  static Future<void> clearVersionCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyWgtVersion);
    await prefs.remove(_keyLastCheckTime);
  }

  /// 获取设备ID
  static Future<String> _getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? '';
      }
      return '';
    } catch (e) {
      print("获取设备ID失败: $e");
      return '';
    }
  }

  /// 上报更新状态
  static Future<void> reportUpdateStatus({
    required String version,
    required WgtUpdateStatus status,
    String? errorMessage,
  }) async {
    try {
      final deviceId = await _getDeviceId();
      final report = WgtUpdateStatusReport(
        appId: _uniAppId,
        version: version,
        deviceId: deviceId,
        platform: WgtApiConfig.getCurrentPlatform(),
        status: status,
        errorMessage: errorMessage,
      );

      await HttpUtil()
          .post(WgtApiConfig.reportStatusUrl, data: report.toJson());
    } catch (e) {
      print("上报更新状态失败: $e");
    }
  }
}

/// wgt更新信息
class WgtUpdateInfo {
  final bool hasUpdate;
  final String currentVersion;
  final String latestVersion;
  final String downloadUrl;
  final String updateDescription;
  final bool forceUpdate;
  final int fileSize;

  WgtUpdateInfo({
    required this.hasUpdate,
    required this.currentVersion,
    required this.latestVersion,
    required this.downloadUrl,
    required this.updateDescription,
    required this.forceUpdate,
    required this.fileSize,
  });

  factory WgtUpdateInfo.fromJson(Map<String, dynamic> json) {
    return WgtUpdateInfo(
      hasUpdate: json['hasUpdate'] ?? false,
      currentVersion: json['currentVersion'] ?? '',
      latestVersion: json['latestVersion'] ?? '',
      downloadUrl: json['downloadUrl'] ?? '',
      updateDescription: json['updateDescription'] ?? '',
      forceUpdate: json['forceUpdate'] ?? false,
      fileSize: json['fileSize'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hasUpdate': hasUpdate,
      'currentVersion': currentVersion,
      'latestVersion': latestVersion,
      'downloadUrl': downloadUrl,
      'updateDescription': updateDescription,
      'forceUpdate': forceUpdate,
      'fileSize': fileSize,
    };
  }
}
