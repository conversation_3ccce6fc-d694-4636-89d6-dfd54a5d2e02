import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:student_end_flutter/utils/wgt_hot_update_manager.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

/// wgt包更新对话框
class WgtUpdateDialog extends StatefulWidget {
  final WgtUpdateInfo updateInfo;
  final VoidCallback? onUpdateComplete;
  
  const WgtUpdateDialog({
    Key? key,
    required this.updateInfo,
    this.onUpdateComplete,
  }) : super(key: key);
  
  @override
  State<WgtUpdateDialog> createState() => _WgtUpdateDialogState();
  
  /// 显示更新对话框
  static Future<void> show(
    BuildContext context,
    WgtUpdateInfo updateInfo, {
    VoidCallback? onUpdateComplete,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: !updateInfo.forceUpdate,
      builder: (BuildContext context) {
        return WgtUpdateDialog(
          updateInfo: updateInfo,
          onUpdateComplete: onUpdateComplete,
        );
      },
    );
  }
}

class _WgtUpdateDialogState extends State<WgtUpdateDialog> {
  bool _isUpdating = false;
  double _progress = 0.0;
  String _statusText = '';
  
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => !widget.updateInfo.forceUpdate && !_isUpdating,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Row(
          children: [
            Icon(
              Icons.system_update,
              color: Theme.of(context).primaryColor,
              size: 24.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              '发现新版本',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 版本信息
            _buildVersionInfo(),
            SizedBox(height: 16.h),
            
            // 更新描述
            if (widget.updateInfo.updateDescription.isNotEmpty) ...[
              Text(
                '更新内容：',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  widget.updateInfo.updateDescription,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              SizedBox(height: 16.h),
            ],
            
            // 进度条
            if (_isUpdating) ...[
              Text(
                _statusText,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8.h),
              LinearProgressIndicator(
                value: _progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                '${(_progress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
        actions: _buildActions(),
      ),
    );
  }
  
  /// 构建版本信息
  Widget _buildVersionInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '当前版本：',
                style: TextStyle(fontSize: 13.sp),
              ),
              Text(
                widget.updateInfo.currentVersion,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Text(
                '最新版本：',
                style: TextStyle(fontSize: 13.sp),
              ),
              Text(
                widget.updateInfo.latestVersion,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          if (widget.updateInfo.fileSize > 0) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Text(
                  '更新包大小：',
                  style: TextStyle(fontSize: 13.sp),
                ),
                Text(
                  _formatFileSize(widget.updateInfo.fileSize),
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建操作按钮
  List<Widget> _buildActions() {
    if (_isUpdating) {
      return [];
    }
    
    List<Widget> actions = [];
    
    // 如果不是强制更新，显示取消按钮
    if (!widget.updateInfo.forceUpdate) {
      actions.add(
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            '稍后更新',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
      );
    }
    
    // 立即更新按钮
    actions.add(
      ElevatedButton(
        onPressed: _startUpdate,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        child: Text(
          '立即更新',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.white,
          ),
        ),
      ),
    );
    
    return actions;
  }
  
  /// 开始更新
  void _startUpdate() async {
    setState(() {
      _isUpdating = true;
      _progress = 0.0;
      _statusText = '准备下载...';
    });
    
    try {
      final success = await WgtHotUpdateManager.downloadAndInstall(
        widget.updateInfo,
        onProgress: (received, total) {
          if (total > 0) {
            setState(() {
              _progress = received / total;
              _statusText = '下载中... ${_formatFileSize(received)}/${_formatFileSize(total)}';
            });
          }
        },
      );
      
      if (success) {
        setState(() {
          _progress = 1.0;
          _statusText = '更新完成';
        });
        
        // 延迟关闭对话框
        await Future.delayed(const Duration(seconds: 1));
        
        if (mounted) {
          Navigator.of(context).pop();
          widget.onUpdateComplete?.call();
        }
      } else {
        setState(() {
          _isUpdating = false;
          _statusText = '更新失败';
        });
      }
    } catch (e) {
      setState(() {
        _isUpdating = false;
        _statusText = '更新失败：$e';
      });
    }
  }
  
  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}
