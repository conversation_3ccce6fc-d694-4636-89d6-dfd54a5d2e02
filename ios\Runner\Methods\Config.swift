//
//  Config.swift
//  StuMetApp
//
//  Created by 许江泉 on 2024/11/11.
//

import Foundation
import UIKit


let APPID = "__UNI__D2FAEA7"
// MARK: - double类型width
/// - double类型width
var ScreenWidth: Double = Double(UIScreen.main.bounds.size.width)
// MARK: - double类型height
/// - double类型height
var ScreenHeight: Double = Double(UIScreen.main.bounds.size.height)
// MARK: - cgfloat类型width
/// - cgfloat类型width
let SRNW: CGFloat = UIScreen.main.bounds.size.width
// MARK: - cgfloat类型height
/// - cgfloat类型height
let SRNH: CGFloat = UIScreen.main.bounds.size.height

// 通知 更新购物车数量appPayCallBack
let APP_PAY_CALLBACK = NSNotification.Name("APP_PAY_CALLBACK")

let appDelegate: AppDelegate = UIApplication.shared.delegate as! AppDelegate


// MARK: - Key window
var appw: UIWindow? {
    return appDelegate.window
}

func getStatusBarHight() -> CGFloat {
    var statusBarHeight: CGFloat = 0
    if #available(iOS 13.0, *) {
        statusBarHeight = UIApplication.shared.windows.first?.windowScene?.statusBarManager?.statusBarFrame.size.height ?? 20
    } else {
        statusBarHeight = UIApplication.shared.statusBarFrame.size.height
    }
    return statusBarHeight
}

