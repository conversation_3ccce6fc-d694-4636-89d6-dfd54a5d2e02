import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../../../components/toast_utils.dart';
import '../../../dialog/dialog_common.dart';
import '../../../utils/app_version_util.dart';
import 'dart:io';

class HomeDrawerController extends GetxController {
  RxBool isNeedUp = false.obs;

  @override
  onInit() {
    super.onInit();
    onClickCheckVer();
  }

  /// 清除缓存方法
  Future<void> clearCache() async {
    DialogUtils.showSimpleDialog(
        title: "提示",
        content: "确定要清除应用缓存吗？",
        leftButtonAction: () {
          // 用户点击取消，不做任何操作
        },
        rightButtonAction: () async {
          try {
            // 1. 清理临时目录（安全方式）
            final tempDir = await getTemporaryDirectory();
            await _deleteDirContents(tempDir); // 仅删除内容，不删目录

            // 2. 清理 Documents 目录（安全方式）
            final docDir = await getApplicationDocumentsDirectory();
            await _deleteDirContents(docDir); // 仅删除内容，不删目录

            // 3. 清理图片缓存
            await DefaultCacheManager().emptyCache();

            ToastUtil.showShortSuccessToast("清理缓存成功");
          } catch (e) {
            debugPrint("清理缓存失败: $e");
            ToastUtil.showShortErrorToast("清理缓存失败");
          }
        });
  }

  /// 安全删除目录内容（保留目录结构）
  Future<void> _deleteDirContents(Directory dir) async {
    if (!await dir.exists()) return;

    final files = dir.list(recursive: true);
    await for (final entity in files) {
      if (entity is File) {
        try {
          await entity.delete();
        } catch (e) {
          debugPrint("删除文件失败: ${entity.path} - $e");
        }
      }
    }
  }

  /// 更新版本
  onClickCheckVer({isShowTips = false}) async {
    isNeedUp.value = await AppVersionUtil.checkVersion(isShowTips: isShowTips);
  }

  /// 检查wgt包更新
  onClickCheckWgtUpdate({isShowTips = false}) async {
    try {
      // 这里可以添加wgt包更新检查逻辑
      // 由于需要BuildContext，建议在UI层调用WgtUpdateHelper
      debugPrint("检查wgt包更新");
    } catch (e) {
      debugPrint("检查wgt包更新失败: $e");
    }
  }
}
