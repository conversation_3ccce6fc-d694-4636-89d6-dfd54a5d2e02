import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
    private let engine = FlutterEngine(name: SDKConfig.engine, project: nil)
    var flutterChannelManager: FlutterChannelManager?
    var paths: String?
    override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        setupUI()
        commonInit()
        setupUniMPSDK(launchOptions)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
}

extension AppDelegate {
    
    /// 设置适配 UITableView
    private func setupUI() {
        if #available(iOS 11, *) { UIScrollView.appearance().contentInsetAdjustmentBehavior = .never }
        if #available(iOS 15.0, *) { UITableView.appearance().sectionHeaderTopPadding = 0.0 }
        engine.run(withEntrypoint: nil)
        let controller = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
        flutterChannelManager = FlutterChannelManager(controller: controller)
        let nav = BaseNavViewController(rootViewController: controller)
        nav.isNavigationBarHidden = true
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.rootViewController = nav
        window?.makeKeyAndVisible()
    }
    
}

extension AppDelegate {
    /// 初始化配置
    private func commonInit() {
        GeneratedPluginRegistrant.register(with: engine)
        WXApi.registerApp(SDKConfig.wechatAppId, universalLink: SDKConfig.baseUniversalLink)
        WXSDKEngine.registerModule(NSStringFromClass(ZXUniModule.self), with: ZXUniModule.self)

        // 注册wgt热更新插件
        WgtHotUpdateChannel.register(with: engine.registrar(forPlugin: "WgtHotUpdateChannel")!)
    }
    

}
extension AppDelegate {
    
    /// 设置小程序sdk
    private func setupUniMPSDK(_ options: [UIApplication.LaunchOptionsKey: Any]?) {
        let options = NSMutableDictionary(dictionary: options ?? [:])
        options.setValue(NSNumber(value: true), forKey: "debug")
        DCUniMPSDKEngine.initSDKEnvironment(launchOptions: options as! [AnyHashable: Any])
        UrlProtocolResolver.checkUniMPResoutce()
    }
}
