# wgt包热更新API接口文档

## 概述

本文档定义了wgt包热更新系统所需的服务端API接口。这些接口用于支持uni-app小程序的热更新功能。

## 基础信息

- **基础URL**: `{Config.URL}/wgt`
- **请求方式**: GET/POST
- **响应格式**: JSON
- **字符编码**: UTF-8

## API接口列表

### 1. 检查wgt包版本更新

**接口地址**: `/check/version`

**请求方式**: GET

**请求参数**:
```
appId: string (必填) - 小程序应用ID，如 "__UNI__201304B"
currentVersion: string (必填) - 当前版本号，如 "1.0.0"
platform: string (可选) - 平台标识，android/ios
deviceId: string (可选) - 设备ID
```

**请求示例**:
```
GET /wgt/check/version?appId=__UNI__201304B&currentVersion=1.0.0&platform=android
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasUpdate": true,
    "currentVersion": "1.0.0",
    "latestVersion": "1.0.1",
    "downloadUrl": "https://example.com/wgt/__UNI__201304B_1.0.1.wgt",
    "updateDescription": "修复已知问题，优化用户体验",
    "forceUpdate": false,
    "fileSize": 2048576,
    "md5": "d41d8cd98f00b204e9800998ecf8427e",
    "publishTime": "2025-01-08 10:00:00"
  }
}
```

**响应字段说明**:
- `hasUpdate`: 是否有更新
- `currentVersion`: 当前版本
- `latestVersion`: 最新版本
- `downloadUrl`: wgt包下载地址
- `updateDescription`: 更新说明
- `forceUpdate`: 是否强制更新
- `fileSize`: 文件大小（字节）
- `md5`: 文件MD5校验值
- `publishTime`: 发布时间

### 2. 下载wgt包

**接口地址**: `/download/{appId}/{version}`

**请求方式**: GET

**请求参数**:
```
appId: string (路径参数) - 小程序应用ID
version: string (路径参数) - 版本号
```

**请求示例**:
```
GET /wgt/download/__UNI__201304B/1.0.1
```

**响应**: 直接返回wgt文件的二进制流

**响应头**:
```
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="__UNI__201304B_1.0.1.wgt"
Content-Length: 2048576
```

### 3. 获取wgt包信息

**接口地址**: `/info/{appId}`

**请求方式**: GET

**请求参数**:
```
appId: string (路径参数) - 小程序应用ID
```

**请求示例**:
```
GET /wgt/info/__UNI__201304B
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "appId": "__UNI__201304B",
    "appName": "鼎教教小程序",
    "currentVersion": "1.0.1",
    "versions": [
      {
        "version": "1.0.1",
        "publishTime": "2025-01-08 10:00:00",
        "description": "修复已知问题，优化用户体验",
        "fileSize": 2048576,
        "downloadCount": 1000
      },
      {
        "version": "1.0.0",
        "publishTime": "2025-01-01 10:00:00",
        "description": "初始版本",
        "fileSize": 2000000,
        "downloadCount": 5000
      }
    ]
  }
}
```

### 4. 上报更新状态

**接口地址**: `/report/status`

**请求方式**: POST

**请求参数**:
```json
{
  "appId": "__UNI__201304B",
  "version": "1.0.1",
  "deviceId": "device123",
  "platform": "android",
  "status": "success",
  "errorMessage": "",
  "updateTime": "2025-01-08 10:30:00"
}
```

**参数说明**:
- `status`: 更新状态，success/failed/downloading/installing
- `errorMessage`: 错误信息（失败时填写）

**响应参数**:
```json
{
  "code": 200,
  "message": "上报成功"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 应用ID不存在 |
| 1002 | 版本不存在 |
| 1003 | 文件不存在 |
| 1004 | 文件校验失败 |

## 服务端实现建议

### 数据库表结构

#### wgt_apps 表（应用信息）
```sql
CREATE TABLE wgt_apps (
  id INT PRIMARY KEY AUTO_INCREMENT,
  app_id VARCHAR(50) UNIQUE NOT NULL,
  app_name VARCHAR(100) NOT NULL,
  description TEXT,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### wgt_versions 表（版本信息）
```sql
CREATE TABLE wgt_versions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  app_id VARCHAR(50) NOT NULL,
  version VARCHAR(20) NOT NULL,
  file_path VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  md5 VARCHAR(32) NOT NULL,
  description TEXT,
  force_update TINYINT DEFAULT 0,
  status TINYINT DEFAULT 1,
  download_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_app_version (app_id, version),
  INDEX idx_app_id (app_id)
);
```

#### wgt_update_logs 表（更新日志）
```sql
CREATE TABLE wgt_update_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  app_id VARCHAR(50) NOT NULL,
  version VARCHAR(20) NOT NULL,
  device_id VARCHAR(100),
  platform VARCHAR(20),
  status VARCHAR(20) NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_app_id (app_id),
  INDEX idx_device_id (device_id)
);
```

### 安全考虑

1. **文件校验**: 使用MD5或SHA256校验文件完整性
2. **访问控制**: 可以添加token验证或IP白名单
3. **限流**: 对下载接口进行限流保护
4. **日志记录**: 记录所有更新操作的日志

### 部署建议

1. **CDN加速**: wgt文件建议使用CDN分发
2. **负载均衡**: API接口支持负载均衡
3. **监控告警**: 监控更新成功率和错误率
4. **备份策略**: 定期备份wgt文件和数据库
