import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../res/colors.dart';

class InputHaveIcon {
  /// 左侧带有图标的input
  inputLeftHaveIcon(
      {height,
      top,
      left,
      right,
      radius,
      radiusColor,
      controller,
      hintText,
      keyboardType = TextInputType.text,
      fillColor,
      cursorColor,
      hintColor,
      labelColor,
      fontSize,
      img,
      imgH,
      imgW,
      isPwd = false,
      maxLength = 50}) {
    return Container(
      height: height,
      margin: EdgeInsets.only(top: top, left: left, right: right),
      padding: EdgeInsets.only(left: 15.w, right: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(radius),
          border: Border.all(color: radiusColor, width: 1),
          color: Colors.white),
      child: Row(
        children: [
          Image(
            image: AssetImage(img),
            height: imgH,
            width: imgW,
            // 添加对齐属性
            alignment: Alignment.center,
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 10.w, right: 10.w),
              height: double.infinity,
              alignment: Alignment.center,
              child: TextField(
                maxLength: maxLength,
                controller: controller,
                cursorColor: cursorColor,
                keyboardType: keyboardType,
                obscureText: isPwd,
                onSubmitted: (String text) {},
                decoration: InputDecoration(
                  counter: SizedBox.shrink(),
                  hintText: hintText,
                  border: InputBorder.none,
                  fillColor: fillColor,
                  hintStyle: TextStyle(
                      fontFamily: "R", color: hintColor, fontSize: fontSize),
                  labelStyle: TextStyle(
                    fontFamily: "R",
                    color: labelColor,
                    fontSize: fontSize,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
