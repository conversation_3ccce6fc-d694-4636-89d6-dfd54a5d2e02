# wgt包热更新使用说明

## 概述

本文档介绍如何在Flutter项目中使用wgt包热更新功能，实现uni-app小程序的热更新。

## 功能特性

- ✅ 自动检查更新
- ✅ 手动检查更新  
- ✅ 下载进度显示
- ✅ 强制更新支持
- ✅ 版本管理
- ✅ 状态上报
- ✅ 错误处理
- ✅ 缓存管理

## 快速开始

### 1. 在应用启动时检查更新

在你的主页面或启动页面中添加以下代码：

```dart
import 'package:student_end_flutter/utils/wgt_update_helper.dart';

class HomePage extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    // 应用启动时检查更新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WgtUpdateHelper.checkUpdateOnAppStart(context);
    });
  }
}
```

### 2. 添加手动检查更新按钮

在设置页面或其他地方添加手动检查更新功能：

```dart
ElevatedButton(
  onPressed: () {
    WgtUpdateHelper.checkUpdateManually(context);
  },
  child: Text('检查更新'),
)
```

### 3. 显示当前版本

```dart
FutureBuilder<String>(
  future: WgtUpdateHelper.getCurrentVersion(),
  builder: (context, snapshot) {
    return Text('当前版本: ${snapshot.data ?? '获取中...'}');
  },
)
```

## 详细使用

### 使用WgtUpdatePage

项目已经提供了一个完整的更新管理页面，可以直接使用：

```dart
import 'package:student_end_flutter/pages/settings/wgt_update_page.dart';

// 导航到更新页面
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => WgtUpdatePage()),
);
```

### 自定义更新检查

如果需要更精细的控制，可以直接使用WgtHotUpdateManager：

```dart
import 'package:student_end_flutter/utils/wgt_hot_update_manager.dart';

// 检查更新
final updateInfo = await WgtHotUpdateManager.checkUpdate(
  forceCheck: true,  // 强制检查，忽略时间间隔
  showToast: true,   // 显示提示信息
);

if (updateInfo != null) {
  // 有更新可用
  print('最新版本: ${updateInfo.latestVersion}');
  print('更新描述: ${updateInfo.updateDescription}');
  print('是否强制更新: ${updateInfo.forceUpdate}');
  
  // 下载并安装
  final success = await WgtHotUpdateManager.downloadAndInstall(
    updateInfo,
    onProgress: (received, total) {
      print('下载进度: ${(received / total * 100).toInt()}%');
    },
  );
  
  if (success) {
    print('更新成功');
  }
}
```

### 自定义更新对话框

如果需要自定义更新对话框的样式，可以参考WgtUpdateDialog的实现：

```dart
import 'package:student_end_flutter/components/wgt_update_dialog.dart';

// 显示更新对话框
WgtUpdateDialog.show(
  context,
  updateInfo,
  onUpdateComplete: () {
    // 更新完成回调
    print('更新完成');
  },
);
```

## 配置说明

### 小程序ID配置

在`lib/utils/wgt_hot_update_manager.dart`中修改小程序ID：

```dart
// 小程序包ID
static const String _uniAppId = "__UNI__201304B";  // 修改为你的小程序ID
```

### API接口配置

在`lib/common/wgt_api_config.dart`中配置API接口：

```dart
/// 基础URL
static String get baseUrl => "${Config.URL}/wgt";  // 修改为你的API地址
```

### 更新配置

使用WgtUpdateConfig类配置更新行为：

```dart
import 'package:student_end_flutter/utils/wgt_update_helper.dart';

// 配置自动检查更新
WgtUpdateConfig.autoCheckUpdate = true;
WgtUpdateConfig.autoCheckInterval = 24;  // 24小时检查一次
WgtUpdateConfig.wifiOnlyDownload = true;  // 仅WiFi下载
```

## 服务端集成

### API接口实现

根据`docs/wgt_hot_update_api.md`文档实现服务端API接口：

1. **检查版本接口**: `GET /wgt/check/version`
2. **下载wgt包接口**: `GET /wgt/download/{appId}/{version}`
3. **上报状态接口**: `POST /wgt/report/status`

### 数据库设计

参考API文档中的数据库表结构设计。

### wgt包管理

1. 将wgt包上传到服务器
2. 在数据库中记录版本信息
3. 配置下载地址和版本信息

## 原生端配置

### Android端

确保在`MainActivity.java`中注册了`WgtHotUpdateChannel`：

```java
flutterEngine.getPlugins().add(new WgtHotUpdateChannel());
```

### iOS端

确保在`AppDelegate.swift`中注册了`WgtHotUpdateChannel`：

```swift
WgtHotUpdateChannel.register(with: engine.registrar(forPlugin: "WgtHotUpdateChannel")!)
```

## 测试

### 测试更新流程

1. 准备两个版本的wgt包
2. 配置服务端API返回更新信息
3. 在应用中触发检查更新
4. 验证下载和安装流程

### 测试强制更新

1. 配置服务端返回`forceUpdate: true`
2. 验证用户无法取消更新对话框
3. 验证更新完成后的行为

### 测试错误处理

1. 测试网络错误情况
2. 测试下载失败情况
3. 测试安装失败情况

## 常见问题

### Q: 更新后小程序没有生效？

A: 需要重新打开小程序才能看到更新效果。可以在更新完成后提示用户重启小程序。

### Q: 如何调试热更新功能？

A: 可以使用以下方法：
1. 查看控制台日志
2. 使用`WgtUpdateHelper.clearUpdateCache()`清除缓存
3. 强制检查更新进行测试

### Q: 如何处理更新失败？

A: 系统会自动上报更新状态，可以在服务端查看失败原因。同时会显示错误提示给用户。

### Q: 如何自定义更新UI？

A: 可以参考`WgtUpdateDialog`和`WgtUpdatePage`的实现，创建自己的更新界面。

## 注意事项

1. **版本号格式**: 建议使用语义化版本号，如`1.0.0`
2. **文件大小**: wgt包不宜过大，建议控制在10MB以内
3. **网络环境**: 考虑用户的网络环境，提供重试机制
4. **用户体验**: 避免在用户使用过程中强制更新
5. **安全性**: 验证wgt包的完整性和来源

## 更新日志

- v1.0.0: 初始版本，支持基本的热更新功能
- v1.0.1: 添加状态上报和错误处理
- v1.0.2: 优化UI界面和用户体验
