import 'package:flutter/material.dart';
import 'package:student_end_flutter/utils/wgt_hot_update_manager.dart';
import 'package:student_end_flutter/components/wgt_update_dialog.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

/// wgt热更新辅助类
/// 提供便捷的热更新功能集成
class WgtUpdateHelper {
  
  /// 应用启动时检查更新
  /// 建议在应用启动后调用此方法
  static Future<void> checkUpdateOnAppStart(BuildContext context) async {
    try {
      // 静默检查更新，不显示提示
      final updateInfo = await WgtHotUpdateManager.checkUpdate(
        forceCheck: false,
        showToast: false,
      );
      
      if (updateInfo != null && context.mounted) {
        // 如果是强制更新，立即显示对话框
        if (updateInfo.forceUpdate) {
          WgtUpdateDialog.show(
            context,
            updateInfo,
            onUpdateComplete: () {
              ToastUtil.showToastText('更新完成，请重启应用');
            },
          );
        } else {
          // 非强制更新，可以延迟显示或者显示通知
          _showUpdateNotification(context, updateInfo);
        }
      }
    } catch (e) {
      print('启动时检查更新失败: $e');
    }
  }
  
  /// 手动检查更新
  /// 用户主动触发的更新检查
  static Future<void> checkUpdateManually(BuildContext context) async {
    try {
      final updateInfo = await WgtHotUpdateManager.checkUpdate(
        forceCheck: true,
        showToast: true,
      );
      
      if (updateInfo != null && context.mounted) {
        WgtUpdateDialog.show(
          context,
          updateInfo,
          onUpdateComplete: () {
            ToastUtil.showToastText('更新完成，请重启小程序');
          },
        );
      }
    } catch (e) {
      ToastUtil.showToastText('检查更新失败：$e');
    }
  }
  
  /// 显示更新通知
  static void _showUpdateNotification(BuildContext context, WgtUpdateInfo updateInfo) {
    // 可以使用SnackBar或其他方式显示更新通知
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('发现新版本 ${updateInfo.latestVersion}'),
        action: SnackBarAction(
          label: '立即更新',
          onPressed: () {
            WgtUpdateDialog.show(
              context,
              updateInfo,
              onUpdateComplete: () {
                ToastUtil.showToastText('更新完成，请重启小程序');
              },
            );
          },
        ),
        duration: const Duration(seconds: 5),
      ),
    );
  }
  
  /// 获取当前版本信息
  static Future<String> getCurrentVersion() async {
    return await WgtHotUpdateManager.getCurrentWgtVersion();
  }
  
  /// 清除更新缓存
  /// 用于调试或重置更新状态
  static Future<void> clearUpdateCache() async {
    await WgtHotUpdateManager.clearVersionCache();
    ToastUtil.showToastText('更新缓存已清除');
  }
}

/// 热更新状态监听器
class WgtUpdateStatusListener {
  static final List<Function(String version, String status)> _listeners = [];
  
  /// 添加状态监听器
  static void addListener(Function(String version, String status) listener) {
    _listeners.add(listener);
  }
  
  /// 移除状态监听器
  static void removeListener(Function(String version, String status) listener) {
    _listeners.remove(listener);
  }
  
  /// 通知状态变化
  static void notifyStatusChange(String version, String status) {
    for (final listener in _listeners) {
      try {
        listener(version, status);
      } catch (e) {
        print('通知状态变化失败: $e');
      }
    }
  }
  
  /// 清除所有监听器
  static void clearListeners() {
    _listeners.clear();
  }
}

/// 热更新配置
class WgtUpdateConfig {
  /// 是否启用自动检查更新
  static bool autoCheckUpdate = true;
  
  /// 自动检查更新的间隔（小时）
  static int autoCheckInterval = 24;
  
  /// 是否仅在WiFi下自动下载
  static bool wifiOnlyDownload = true;
  
  /// 是否显示更新进度
  static bool showUpdateProgress = true;
  
  /// 最大重试次数
  static int maxRetryCount = 3;
  
  /// 从SharedPreferences加载配置
  static Future<void> loadConfig() async {
    // TODO: 从SharedPreferences加载配置
  }
  
  /// 保存配置到SharedPreferences
  static Future<void> saveConfig() async {
    // TODO: 保存配置到SharedPreferences
  }
}
