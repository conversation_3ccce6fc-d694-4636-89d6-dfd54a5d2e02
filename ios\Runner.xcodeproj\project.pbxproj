// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		1D3B600BB4059A14C84F4B38 /* libPods-RunnerTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 467178B873D056347844D630 /* libPods-RunnerTests.a */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		4B1D64B92E0EB72100A6C20F /* __UNI__D2FAEA7.wgt in Resources */ = {isa = PBXBuildFile; fileRef = 4B1D64B82E0EB72100A6C20F /* __UNI__D2FAEA7.wgt */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		8E0309CC2D9B79A6004C32B9 /* Enum.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309CB2D9B79A6004C32B9 /* Enum.swift */; };
		8E0309CE2D9B79D1004C32B9 /* UrlProtocolResolver.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309CD2D9B79D1004C32B9 /* UrlProtocolResolver.swift */; };
		8E0309D12D9B7ADC004C32B9 /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309D02D9B7ADC004C32B9 /* Config.swift */; };
		8E0309D52D9B7B9D004C32B9 /* UIKit+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309D42D9B7B9D004C32B9 /* UIKit+Extension.swift */; };
		8E0309D72D9B7BF4004C32B9 /* SDKConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309D62D9B7BF4004C32B9 /* SDKConfig.swift */; };
		8E0309DA2D9B7D11004C32B9 /* BaseNavViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E0309D92D9B7D11004C32B9 /* BaseNavViewController.swift */; };
		8E0309DE2D9B80F8004C32B9 /* liblibIO.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E0309DB2D9B80F8004C32B9 /* liblibIO.a */; };
		8E1CDCF12D8AA53000063274 /* AppDelegate + UniMP .swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCF02D8AA53000063274 /* AppDelegate + UniMP .swift */; };
		8E1CDD032D8AA5CE00063274 /* ZXUniModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCFA2D8AA5CD00063274 /* ZXUniModule.m */; };
		8E1CDD042D8AA5CE00063274 /* MiniProgramBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCF22D8AA5CD00063274 /* MiniProgramBridge.swift */; };
		8E1CDD052D8AA5CE00063274 /* PayModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCF42D8AA5CD00063274 /* PayModel.swift */; };
		8E1CDD062D8AA5CE00063274 /* PayParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCF52D8AA5CD00063274 /* PayParam.swift */; };
		8E1CDD072D8AA5CE00063274 /* ShareModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E1CDCF72D8AA5CD00063274 /* ShareModel.swift */; };
		8E296BC22D59E29000AE45BE /* FlutterChannelManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */; };
		8E50F6282D72DECA0095D0C6 /* CurriculumParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */; };
		8E706CE02D76E7FD00284B85 /* QYParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E706CDF2D76E7FD00284B85 /* QYParam.swift */; };
		8E9D98142D9A9DA6004EA962 /* DCMediaEditingController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8E9D980F2D9A9DA6004EA962 /* DCMediaEditingController.bundle */; };
		8E9D98152D9A9DA6004EA962 /* DCTZImagePickerController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8E9D98102D9A9DA6004EA962 /* DCTZImagePickerController.bundle */; };
		8E9D98162D9A9DA6004EA962 /* liblibCamera.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D980D2D9A9DA6004EA962 /* liblibCamera.a */; };
		8E9D98182D9A9DDD004EA962 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D98172D9A9DDD004EA962 /* GLKit.framework */; };
		8E9D981A2D9A9DEC004EA962 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D98192D9A9DEC004EA962 /* MetalKit.framework */; };
		8E9D981C2D9A9E02004EA962 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D981B2D9A9E02004EA962 /* CoreMedia.framework */; };
		8E9D981E2D9A9E0F004EA962 /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D981D2D9A9E0F004EA962 /* Photos.framework */; };
		8E9D98202D9A9E1C004EA962 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E9D981F2D9A9E1C004EA962 /* AssetsLibrary.framework */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		9D6F0BA524E19B23E75BE0FA /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CACD1895743358989102DBF9 /* Pods_Runner.framework */; };
		FE761F752E06D1780090B7BD /* AppDelegate + Push.swift in Sources */ = {isa = PBXBuildFile; fileRef = FE761F742E06D1780090B7BD /* AppDelegate + Push.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		03A838E3967BF9B8A392461A /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3BEC83AD05E7305C906EAA4D /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		467178B873D056347844D630 /* libPods-RunnerTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RunnerTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		4B1D64B82E0EB72100A6C20F /* __UNI__D2FAEA7.wgt */ = {isa = PBXFileReference; lastKnownFileType = file; path = __UNI__D2FAEA7.wgt; sourceTree = "<group>"; };
		505DB34770A90915EC60B48E /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		6CBB1B16E544E9F59ABCB5C3 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		8E0309CB2D9B79A6004C32B9 /* Enum.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enum.swift; sourceTree = "<group>"; };
		8E0309CD2D9B79D1004C32B9 /* UrlProtocolResolver.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UrlProtocolResolver.swift; sourceTree = "<group>"; };
		8E0309D02D9B7ADC004C32B9 /* Config.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Config.swift; sourceTree = "<group>"; };
		8E0309D42D9B7B9D004C32B9 /* UIKit+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIKit+Extension.swift"; sourceTree = "<group>"; };
		8E0309D62D9B7BF4004C32B9 /* SDKConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SDKConfig.swift; sourceTree = "<group>"; };
		8E0309D92D9B7D11004C32B9 /* BaseNavViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseNavViewController.swift; sourceTree = "<group>"; };
		8E0309DB2D9B80F8004C32B9 /* liblibIO.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibIO.a; sourceTree = "<group>"; };
		8E1CDCF02D8AA53000063274 /* AppDelegate + UniMP .swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate + UniMP .swift"; sourceTree = "<group>"; };
		8E1CDCF22D8AA5CD00063274 /* MiniProgramBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MiniProgramBridge.swift; sourceTree = "<group>"; };
		8E1CDCF42D8AA5CD00063274 /* PayModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PayModel.swift; sourceTree = "<group>"; };
		8E1CDCF52D8AA5CD00063274 /* PayParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PayParam.swift; sourceTree = "<group>"; };
		8E1CDCF72D8AA5CD00063274 /* ShareModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareModel.swift; sourceTree = "<group>"; };
		8E1CDCF92D8AA5CD00063274 /* ZXUniModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZXUniModule.h; sourceTree = "<group>"; };
		8E1CDCFA2D8AA5CD00063274 /* ZXUniModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZXUniModule.m; sourceTree = "<group>"; };
		8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlutterChannelManager.swift; sourceTree = "<group>"; };
		8E3262682D6C59F9000DCC56 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		8E3F4AEF2D576BED00217B2D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurriculumParam.swift; sourceTree = "<group>"; };
		8E6565652D642EF800466B72 /* RunnerProfile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerProfile.entitlements; sourceTree = "<group>"; };
		8E706CDF2D76E7FD00284B85 /* QYParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QYParam.swift; sourceTree = "<group>"; };
		8E9D980D2D9A9DA6004EA962 /* liblibCamera.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibCamera.a; sourceTree = "<group>"; };
		8E9D980F2D9A9DA6004EA962 /* DCMediaEditingController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCMediaEditingController.bundle; sourceTree = "<group>"; };
		8E9D98102D9A9DA6004EA962 /* DCTZImagePickerController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCTZImagePickerController.bundle; sourceTree = "<group>"; };
		8E9D98172D9A9DDD004EA962 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		8E9D98192D9A9DEC004EA962 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		8E9D981B2D9A9E02004EA962 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		8E9D981D2D9A9E0F004EA962 /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		8E9D981F2D9A9E1C004EA962 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		951F01B594C2542E19F293EE /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		AEAA6BC94A9C4C0156A4E812 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		CACD1895743358989102DBF9 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FE761F742E06D1780090B7BD /* AppDelegate + Push.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate + Push.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		5AEC1C7F33D45DACF507C2E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1D3B600BB4059A14C84F4B38 /* libPods-RunnerTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E9D98202D9A9E1C004EA962 /* AssetsLibrary.framework in Frameworks */,
				8E9D981E2D9A9E0F004EA962 /* Photos.framework in Frameworks */,
				8E9D981C2D9A9E02004EA962 /* CoreMedia.framework in Frameworks */,
				8E9D981A2D9A9DEC004EA962 /* MetalKit.framework in Frameworks */,
				8E9D98182D9A9DDD004EA962 /* GLKit.framework in Frameworks */,
				8E9D98162D9A9DA6004EA962 /* liblibCamera.a in Frameworks */,
				8E0309DE2D9B80F8004C32B9 /* liblibIO.a in Frameworks */,
				9D6F0BA524E19B23E75BE0FA /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		80F5943DC9F9E118851C5E3D /* Pods */ = {
			isa = PBXGroup;
			children = (
				951F01B594C2542E19F293EE /* Pods-Runner.debug.xcconfig */,
				6CBB1B16E544E9F59ABCB5C3 /* Pods-Runner.release.xcconfig */,
				03A838E3967BF9B8A392461A /* Pods-Runner.profile.xcconfig */,
				AEAA6BC94A9C4C0156A4E812 /* Pods-RunnerTests.debug.xcconfig */,
				505DB34770A90915EC60B48E /* Pods-RunnerTests.release.xcconfig */,
				3BEC83AD05E7305C906EAA4D /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		8E0309CA2D9B799B004C32B9 /* Methods */ = {
			isa = PBXGroup;
			children = (
				8E0309D62D9B7BF4004C32B9 /* SDKConfig.swift */,
				8E0309D02D9B7ADC004C32B9 /* Config.swift */,
				8E0309CB2D9B79A6004C32B9 /* Enum.swift */,
			);
			path = Methods;
			sourceTree = "<group>";
		};
		8E0309CF2D9B79E9004C32B9 /* UserManger */ = {
			isa = PBXGroup;
			children = (
				8E0309CD2D9B79D1004C32B9 /* UrlProtocolResolver.swift */,
			);
			path = UserManger;
			sourceTree = "<group>";
		};
		8E0309D32D9B7B89004C32B9 /* Extension */ = {
			isa = PBXGroup;
			children = (
				8E0309D42D9B7B9D004C32B9 /* UIKit+Extension.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		8E0309D82D9B7D06004C32B9 /* BaseController */ = {
			isa = PBXGroup;
			children = (
				8E0309D92D9B7D11004C32B9 /* BaseNavViewController.swift */,
			);
			path = BaseController;
			sourceTree = "<group>";
		};
		8E0309DC2D9B80F8004C32B9 /* Libs */ = {
			isa = PBXGroup;
			children = (
				8E0309DB2D9B80F8004C32B9 /* liblibIO.a */,
			);
			path = Libs;
			sourceTree = "<group>";
		};
		8E0309DD2D9B80F8004C32B9 /* File */ = {
			isa = PBXGroup;
			children = (
				8E0309DC2D9B80F8004C32B9 /* Libs */,
			);
			path = File;
			sourceTree = "<group>";
		};
		8E1CDCF32D8AA5CD00063274 /* MiniProgramBridge */ = {
			isa = PBXGroup;
			children = (
				8E1CDCF22D8AA5CD00063274 /* MiniProgramBridge.swift */,
			);
			path = MiniProgramBridge;
			sourceTree = "<group>";
		};
		8E1CDCF62D8AA5CD00063274 /* Pay */ = {
			isa = PBXGroup;
			children = (
				8E1CDCF42D8AA5CD00063274 /* PayModel.swift */,
				8E1CDCF52D8AA5CD00063274 /* PayParam.swift */,
			);
			path = Pay;
			sourceTree = "<group>";
		};
		8E1CDCF82D8AA5CD00063274 /* Share */ = {
			isa = PBXGroup;
			children = (
				8E1CDCF72D8AA5CD00063274 /* ShareModel.swift */,
			);
			path = Share;
			sourceTree = "<group>";
		};
		8E1CDCFB2D8AA5CD00063274 /* UniMPModule */ = {
			isa = PBXGroup;
			children = (
				8E1CDCF32D8AA5CD00063274 /* MiniProgramBridge */,
				8E1CDCF62D8AA5CD00063274 /* Pay */,
				8E1CDCF82D8AA5CD00063274 /* Share */,
				8E1CDCF92D8AA5CD00063274 /* ZXUniModule.h */,
				8E1CDCFA2D8AA5CD00063274 /* ZXUniModule.m */,
			);
			path = UniMPModule;
			sourceTree = "<group>";
		};
		8E296BC02D59E26D00AE45BE /* FlutterChannelManager */ = {
			isa = PBXGroup;
			children = (
				8E706CDE2D76E7D300284B85 /* QY */,
				8E50F6262D72DEA50095D0C6 /* Curriculum */,
				8E296BC12D59E29000AE45BE /* FlutterChannelManager.swift */,
			);
			path = FlutterChannelManager;
			sourceTree = "<group>";
		};
		8E50F6262D72DEA50095D0C6 /* Curriculum */ = {
			isa = PBXGroup;
			children = (
				8E50F6272D72DECA0095D0C6 /* CurriculumParam.swift */,
			);
			path = Curriculum;
			sourceTree = "<group>";
		};
		8E706CDE2D76E7D300284B85 /* QY */ = {
			isa = PBXGroup;
			children = (
				8E706CDF2D76E7FD00284B85 /* QYParam.swift */,
			);
			path = QY;
			sourceTree = "<group>";
		};
		8E9D980C2D9A9DA6004EA962 /* Apps */ = {
			isa = PBXGroup;
			children = (
				4B1D64B82E0EB72100A6C20F /* __UNI__D2FAEA7.wgt */,
			);
			path = Apps;
			sourceTree = "<group>";
		};
		8E9D980E2D9A9DA6004EA962 /* Lib */ = {
			isa = PBXGroup;
			children = (
				8E9D980D2D9A9DA6004EA962 /* liblibCamera.a */,
			);
			path = Lib;
			sourceTree = "<group>";
		};
		8E9D98112D9A9DA6004EA962 /* Resources */ = {
			isa = PBXGroup;
			children = (
				8E9D980F2D9A9DA6004EA962 /* DCMediaEditingController.bundle */,
				8E9D98102D9A9DA6004EA962 /* DCTZImagePickerController.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		8E9D98122D9A9DA6004EA962 /* Features */ = {
			isa = PBXGroup;
			children = (
				8E0309DD2D9B80F8004C32B9 /* File */,
				8E9D980E2D9A9DA6004EA962 /* Lib */,
				8E9D98112D9A9DA6004EA962 /* Resources */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		8E9D98132D9A9DA6004EA962 /* UniMPSDK */ = {
			isa = PBXGroup;
			children = (
				8E9D980C2D9A9DA6004EA962 /* Apps */,
				8E9D98122D9A9DA6004EA962 /* Features */,
			);
			path = UniMPSDK;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				80F5943DC9F9E118851C5E3D /* Pods */,
				FF7AEE895563F6ACD2AEDEBE /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				8E3262682D6C59F9000DCC56 /* Runner.entitlements */,
				8E6565652D642EF800466B72 /* RunnerProfile.entitlements */,
				8E0309D82D9B7D06004C32B9 /* BaseController */,
				8E0309CA2D9B799B004C32B9 /* Methods */,
				8E0309D32D9B7B89004C32B9 /* Extension */,
				8E0309CF2D9B79E9004C32B9 /* UserManger */,
				8E9D98132D9A9DA6004EA962 /* UniMPSDK */,
				8E1CDCFB2D8AA5CD00063274 /* UniMPModule */,
				8E296BC02D59E26D00AE45BE /* FlutterChannelManager */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				FE761F742E06D1780090B7BD /* AppDelegate + Push.swift */,
				8E1CDCF02D8AA53000063274 /* AppDelegate + UniMP .swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				8E3F4AEF2D576BED00217B2D /* Info.plist */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		FF7AEE895563F6ACD2AEDEBE /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8E9D981F2D9A9E1C004EA962 /* AssetsLibrary.framework */,
				8E9D981D2D9A9E0F004EA962 /* Photos.framework */,
				8E9D981B2D9A9E02004EA962 /* CoreMedia.framework */,
				8E9D98192D9A9DEC004EA962 /* MetalKit.framework */,
				8E9D98172D9A9DDD004EA962 /* GLKit.framework */,
				CACD1895743358989102DBF9 /* Pods_Runner.framework */,
				467178B873D056347844D630 /* libPods-RunnerTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				4E575CC184F87A1A057959A6 /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				5AEC1C7F33D45DACF507C2E5 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				981A3BA05B03AC4DBB106439 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				CBE00D859AFC4489B1A4A7E7 /* [CP] Embed Pods Frameworks */,
				7030EDF7A1EF62027D4043ED /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B1D64B92E0EB72100A6C20F /* __UNI__D2FAEA7.wgt in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				8E9D98142D9A9DA6004EA962 /* DCMediaEditingController.bundle in Resources */,
				8E9D98152D9A9DA6004EA962 /* DCTZImagePickerController.bundle in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		4E575CC184F87A1A057959A6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7030EDF7A1EF62027D4043ED /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		981A3BA05B03AC4DBB106439 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		CBE00D859AFC4489B1A4A7E7 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FE761F752E06D1780090B7BD /* AppDelegate + Push.swift in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				8E0309D12D9B7ADC004C32B9 /* Config.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				8E1CDCF12D8AA53000063274 /* AppDelegate + UniMP .swift in Sources */,
				8E1CDD032D8AA5CE00063274 /* ZXUniModule.m in Sources */,
				8E0309DA2D9B7D11004C32B9 /* BaseNavViewController.swift in Sources */,
				8E0309D72D9B7BF4004C32B9 /* SDKConfig.swift in Sources */,
				8E1CDD042D8AA5CE00063274 /* MiniProgramBridge.swift in Sources */,
				8E1CDD052D8AA5CE00063274 /* PayModel.swift in Sources */,
				8E0309CC2D9B79A6004C32B9 /* Enum.swift in Sources */,
				8E1CDD062D8AA5CE00063274 /* PayParam.swift in Sources */,
				8E0309CE2D9B79D1004C32B9 /* UrlProtocolResolver.swift in Sources */,
				8E1CDD072D8AA5CE00063274 /* ShareModel.swift in Sources */,
				8E296BC22D59E29000AE45BE /* FlutterChannelManager.swift in Sources */,
				8E706CE02D76E7FD00284B85 /* QYParam.swift in Sources */,
				8E0309D52D9B7B9D004C32B9 /* UIKit+Extension.swift in Sources */,
				8E50F6282D72DECA0095D0C6 /* CurriculumParam.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerProfile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 13;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = profile;
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎教教";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/File/Libs",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/File/Libs",
				);
				MARKETING_VERSION = 2.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AEAA6BC94A9C4C0156A4E812 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 505DB34770A90915EC60B48E /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3BEC83AD05E7305C906EAA4D /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 13;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = debug;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎教教";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/File/Libs",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/File/Libs",
				);
				MARKETING_VERSION = 2.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 13;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JGLLG8H5YW;
				ENABLE_BITCODE = "$(inherited)";
				FLUTTER_BUILD_MODE = release;
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "鼎教教";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/UniMPSDK/File/Libs",
					"$(PROJECT_DIR)/Runner/OpenSDK",
					"$(PROJECT_DIR)/Runner/Vender/OpenSDK",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/File/Libs",
					"$(PROJECT_DIR)/Runner/UniMPSDK\\ 2/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/Lib",
					"$(PROJECT_DIR)/Runner/UniMPSDK/Features/File/Libs",
				);
				MARKETING_VERSION = 2.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dxznjy.deliver;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_COMPILATION_MODE = singlefile;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
