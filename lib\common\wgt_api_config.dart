import 'package:student_end_flutter/common/Config.dart';

/// wgt包热更新API配置
class WgtApiConfig {
  /// 基础URL
  static String get baseUrl => "${Config.URL}/wgt";
  
  /// 检查版本更新接口
  /// GET /wgt/check/version?appId={appId}&currentVersion={version}&platform={platform}
  static String checkVersionUrl({
    required String appId,
    required String currentVersion,
    String? platform,
    String? deviceId,
  }) {
    final params = <String, String>{
      'appId': appId,
      'currentVersion': currentVersion,
    };
    
    if (platform != null) {
      params['platform'] = platform;
    }
    
    if (deviceId != null) {
      params['deviceId'] = deviceId;
    }
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return "$baseUrl/check/version?$queryString";
  }
  
  /// 下载wgt包接口
  /// GET /wgt/download/{appId}/{version}
  static String downloadUrl({
    required String appId,
    required String version,
  }) {
    return "$baseUrl/download/$appId/$version";
  }
  
  /// 获取wgt包信息接口
  /// GET /wgt/info/{appId}
  static String infoUrl({
    required String appId,
  }) {
    return "$baseUrl/info/$appId";
  }
  
  /// 上报更新状态接口
  /// POST /wgt/report/status
  static String get reportStatusUrl => "$baseUrl/report/status";
  
  /// 获取当前平台标识
  static String getCurrentPlatform() {
    // 这里可以根据实际需要返回平台标识
    // 由于Flutter是跨平台的，可以通过Platform.isAndroid等判断
    return 'flutter';
  }
}

/// wgt更新状态枚举
enum WgtUpdateStatus {
  /// 下载中
  downloading('downloading'),
  /// 安装中
  installing('installing'),
  /// 成功
  success('success'),
  /// 失败
  failed('failed');
  
  const WgtUpdateStatus(this.value);
  final String value;
}

/// 上报更新状态的请求体
class WgtUpdateStatusReport {
  final String appId;
  final String version;
  final String? deviceId;
  final String platform;
  final WgtUpdateStatus status;
  final String? errorMessage;
  final DateTime updateTime;
  
  WgtUpdateStatusReport({
    required this.appId,
    required this.version,
    this.deviceId,
    required this.platform,
    required this.status,
    this.errorMessage,
    DateTime? updateTime,
  }) : updateTime = updateTime ?? DateTime.now();
  
  Map<String, dynamic> toJson() {
    return {
      'appId': appId,
      'version': version,
      'deviceId': deviceId,
      'platform': platform,
      'status': status.value,
      'errorMessage': errorMessage,
      'updateTime': updateTime.toIso8601String(),
    };
  }
}
