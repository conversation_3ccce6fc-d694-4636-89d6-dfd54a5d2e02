import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/utils/wgt_hot_update_manager.dart';
import 'package:student_end_flutter/components/wgt_update_dialog.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

/// wgt包热更新管理页面
class WgtUpdatePage extends StatefulWidget {
  const WgtUpdatePage({Key? key}) : super(key: key);

  @override
  State<WgtUpdatePage> createState() => _WgtUpdatePageState();
}

class _WgtUpdatePageState extends State<WgtUpdatePage> {
  String _currentVersion = '';
  bool _isChecking = false;
  DateTime? _lastCheckTime;

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
    _loadLastCheckTime();
  }

  /// 加载当前版本
  void _loadCurrentVersion() async {
    try {
      final version = await WgtHotUpdateManager.getCurrentWgtVersion();
      setState(() {
        _currentVersion = version;
      });
    } catch (e) {
      print('加载当前版本失败: $e');
    }
  }

  /// 加载最后检查时间
  void _loadLastCheckTime() async {
    // 这里可以从SharedPreferences获取最后检查时间
    // 暂时使用模拟数据
    setState(() {
      _lastCheckTime = DateTime.now().subtract(const Duration(hours: 2));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('小程序更新'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前版本信息卡片
            _buildVersionCard(),
            SizedBox(height: 16.h),
            
            // 更新检查卡片
            _buildUpdateCheckCard(),
            SizedBox(height: 16.h),
            
            // 更新设置卡片
            _buildUpdateSettingsCard(),
            SizedBox(height: 16.h),
            
            // 更新历史卡片
            _buildUpdateHistoryCard(),
          ],
        ),
      ),
    );
  }

  /// 构建版本信息卡片
  Widget _buildVersionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '版本信息',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            _buildInfoRow('当前版本', _currentVersion.isNotEmpty ? _currentVersion : '获取中...'),
            SizedBox(height: 8.h),
            _buildInfoRow('小程序ID', '__UNI__201304B'),
            SizedBox(height: 8.h),
            _buildInfoRow('更新方式', '热更新'),
          ],
        ),
      ),
    );
  }

  /// 构建更新检查卡片
  Widget _buildUpdateCheckCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.system_update,
                  color: Colors.green,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '检查更新',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            if (_lastCheckTime != null) ...[
              _buildInfoRow('最后检查', _formatDateTime(_lastCheckTime!)),
              SizedBox(height: 12.h),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isChecking ? null : _checkUpdate,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
                child: _isChecking
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            '检查中...',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      )
                    : Text(
                        '检查更新',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建更新设置卡片
  Widget _buildUpdateSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.orange,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '更新设置',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            _buildSettingItem(
              '自动检查更新',
              '应用启动时自动检查更新',
              true,
              (value) {
                // TODO: 保存设置
              },
            ),
            _buildSettingItem(
              'WiFi下自动下载',
              '仅在WiFi环境下自动下载更新',
              true,
              (value) {
                // TODO: 保存设置
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建更新历史卡片
  Widget _buildUpdateHistoryCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Colors.purple,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '更新历史',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            // 这里可以显示更新历史记录
            Text(
              '暂无更新记录',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingItem(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  /// 检查更新
  void _checkUpdate() async {
    setState(() {
      _isChecking = true;
    });

    try {
      final updateInfo = await WgtHotUpdateManager.checkUpdate(
        forceCheck: true,
        showToast: false,
      );

      setState(() {
        _lastCheckTime = DateTime.now();
      });

      if (updateInfo != null) {
        // 显示更新对话框
        WgtUpdateDialog.show(
          context,
          updateInfo,
          onUpdateComplete: () {
            _loadCurrentVersion();
            ToastUtil.showToastText('更新完成，请重启小程序');
          },
        );
      } else {
        ToastUtil.showToastText('当前已是最新版本');
      }
    } catch (e) {
      ToastUtil.showToastText('检查更新失败：$e');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
  }
}
