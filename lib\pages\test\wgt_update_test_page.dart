import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:student_end_flutter/utils/wgt_hot_update_manager.dart';
import 'package:student_end_flutter/components/wgt_update_dialog.dart';
import 'package:student_end_flutter/components/toast_utils.dart';

/// wgt热更新测试页面
/// 用于测试热更新功能是否正常工作
class WgtUpdateTestPage extends StatefulWidget {
  const WgtUpdateTestPage({Key? key}) : super(key: key);

  @override
  State<WgtUpdateTestPage> createState() => _WgtUpdateTestPageState();
}

class _WgtUpdateTestPageState extends State<WgtUpdateTestPage> {
  String _currentVersion = '';
  String _testResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
  }

  void _loadCurrentVersion() async {
    try {
      final version = await WgtHotUpdateManager.getCurrentWgtVersion();
      setState(() {
        _currentVersion = version;
      });
    } catch (e) {
      setState(() {
        _currentVersion = '获取失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('wgt热更新测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前状态卡片
            _buildStatusCard(),
            SizedBox(height: 16.h),
            
            // 测试功能卡片
            _buildTestCard(),
            SizedBox(height: 16.h),
            
            // 测试结果卡片
            _buildResultCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '当前状态',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            _buildInfoRow('当前版本', _currentVersion),
            SizedBox(height: 8.h),
            _buildInfoRow('小程序ID', '__UNI__201304B'),
            SizedBox(height: 8.h),
            _buildInfoRow('平台', 'Android/iOS'),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.science,
                  color: Colors.green,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '测试功能',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // 测试按钮网格
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: 2.5,
              children: [
                _buildTestButton(
                  '获取版本',
                  Icons.info,
                  Colors.blue,
                  _testGetVersion,
                ),
                _buildTestButton(
                  '检查更新',
                  Icons.system_update,
                  Colors.orange,
                  _testCheckUpdate,
                ),
                _buildTestButton(
                  '模拟下载',
                  Icons.download,
                  Colors.green,
                  _testDownload,
                ),
                _buildTestButton(
                  '清除缓存',
                  Icons.clear,
                  Colors.red,
                  _testClearCache,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assignment,
                  color: Colors.purple,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '测试结果',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              min: 100.h,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _testResult.isEmpty ? '点击上方按钮开始测试...' : _testResult,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontFamily: 'monospace',
                  color: _testResult.isEmpty ? Colors.grey[600] : Colors.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: _isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 18.sp,
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  void _testGetVersion() async {
    setState(() {
      _isLoading = true;
      _testResult = '正在获取版本信息...';
    });

    try {
      final version = await WgtHotUpdateManager.getCurrentWgtVersion();
      setState(() {
        _testResult = '✅ 获取版本成功\n当前版本: $version\n时间: ${DateTime.now()}';
        _currentVersion = version;
      });
    } catch (e) {
      setState(() {
        _testResult = '❌ 获取版本失败\n错误: $e\n时间: ${DateTime.now()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testCheckUpdate() async {
    setState(() {
      _isLoading = true;
      _testResult = '正在检查更新...';
    });

    try {
      final updateInfo = await WgtHotUpdateManager.checkUpdate(
        forceCheck: true,
        showToast: false,
      );

      if (updateInfo != null) {
        setState(() {
          _testResult = '✅ 检查更新成功\n'
              '有新版本可用\n'
              '当前版本: ${updateInfo.currentVersion}\n'
              '最新版本: ${updateInfo.latestVersion}\n'
              '强制更新: ${updateInfo.forceUpdate}\n'
              '文件大小: ${updateInfo.fileSize} bytes\n'
              '时间: ${DateTime.now()}';
        });
      } else {
        setState(() {
          _testResult = '✅ 检查更新成功\n'
              '当前已是最新版本\n'
              '时间: ${DateTime.now()}';
        });
      }
    } catch (e) {
      setState(() {
        _testResult = '❌ 检查更新失败\n错误: $e\n时间: ${DateTime.now()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testDownload() async {
    setState(() {
      _isLoading = true;
      _testResult = '正在模拟下载...';
    });

    try {
      // 模拟下载过程
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 200));
        setState(() {
          _testResult = '📥 模拟下载中...\n进度: $i%\n时间: ${DateTime.now()}';
        });
      }

      setState(() {
        _testResult = '✅ 模拟下载完成\n'
            '注意: 这只是UI测试\n'
            '实际下载需要服务端支持\n'
            '时间: ${DateTime.now()}';
      });
    } catch (e) {
      setState(() {
        _testResult = '❌ 模拟下载失败\n错误: $e\n时间: ${DateTime.now()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testClearCache() async {
    setState(() {
      _isLoading = true;
      _testResult = '正在清除缓存...';
    });

    try {
      await WgtHotUpdateManager.clearVersionCache();
      setState(() {
        _testResult = '✅ 清除缓存成功\n'
            '版本缓存已清除\n'
            '时间: ${DateTime.now()}';
        _currentVersion = '';
      });
      
      // 重新加载版本
      _loadCurrentVersion();
    } catch (e) {
      setState(() {
        _testResult = '❌ 清除缓存失败\n错误: $e\n时间: ${DateTime.now()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
